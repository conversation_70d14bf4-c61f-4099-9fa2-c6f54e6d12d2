/**
 * Axis-Aligned Bounding Box collision detection utilities
 */

export class AABB {
    constructor(x, y, width, height) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
    }
    
    get left() { return this.x; }
    get right() { return this.x + this.width; }
    get top() { return this.y; }
    get bottom() { return this.y + this.height; }
    get centerX() { return this.x + this.width / 2; }
    get centerY() { return this.y + this.height / 2; }
    
    /**
     * Check if this AABB intersects with another AABB
     */
    intersects(other) {
        return !(this.right <= other.left || 
                this.left >= other.right || 
                this.bottom <= other.top || 
                this.top >= other.bottom);
    }
    
    /**
     * Check if this AABB contains a point
     */
    containsPoint(x, y) {
        return x >= this.left && x <= this.right && 
               y >= this.top && y <= this.bottom;
    }
    
    /**
     * Get the overlap area with another AABB
     */
    getOverlap(other) {
        if (!this.intersects(other)) {
            return { x: 0, y: 0, width: 0, height: 0 };
        }
        
        const left = Math.max(this.left, other.left);
        const right = Math.min(this.right, other.right);
        const top = Math.max(this.top, other.top);
        const bottom = Math.min(this.bottom, other.bottom);
        
        return {
            x: left,
            y: top,
            width: right - left,
            height: bottom - top
        };
    }
    
    /**
     * Get the minimum translation vector to separate from another AABB
     */
    getMTV(other) {
        const overlap = this.getOverlap(other);
        if (overlap.width === 0 || overlap.height === 0) {
            return { x: 0, y: 0 };
        }
        
        // Choose the axis with minimum overlap
        if (overlap.width < overlap.height) {
            // Separate horizontally
            const direction = this.centerX < other.centerX ? -1 : 1;
            return { x: overlap.width * direction, y: 0 };
        } else {
            // Separate vertically
            const direction = this.centerY < other.centerY ? -1 : 1;
            return { x: 0, y: overlap.height * direction };
        }
    }
    
    /**
     * Create a copy of this AABB
     */
    clone() {
        return new AABB(this.x, this.y, this.width, this.height);
    }
    
    /**
     * Move this AABB by the given offset
     */
    translate(dx, dy) {
        this.x += dx;
        this.y += dy;
        return this;
    }
    
    /**
     * Set the position of this AABB
     */
    setPosition(x, y) {
        this.x = x;
        this.y = y;
        return this;
    }
    
    /**
     * Expand this AABB by the given amount in all directions
     */
    expand(amount) {
        this.x -= amount;
        this.y -= amount;
        this.width += amount * 2;
        this.height += amount * 2;
        return this;
    }
}

/**
 * Swept AABB collision detection for moving objects
 */
export class SweptAABB {
    /**
     * Test collision between a moving AABB and a static AABB
     * Returns collision info with time, normal, and contact point
     */
    static test(movingBox, staticBox, velocity) {
        // If not moving, check for overlap
        if (velocity.x === 0 && velocity.y === 0) {
            if (movingBox.intersects(staticBox)) {
                return {
                    hit: true,
                    time: 0,
                    normal: { x: 0, y: 0 },
                    contact: { x: movingBox.centerX, y: movingBox.centerY }
                };
            }
            return { hit: false, time: 1 };
        }
        
        // Calculate entry and exit times for each axis
        let xEntry, xExit, yEntry, yExit;
        
        if (velocity.x > 0) {
            xEntry = staticBox.left - movingBox.right;
            xExit = staticBox.right - movingBox.left;
        } else {
            xEntry = staticBox.right - movingBox.left;
            xExit = staticBox.left - movingBox.right;
        }
        
        if (velocity.y > 0) {
            yEntry = staticBox.top - movingBox.bottom;
            yExit = staticBox.bottom - movingBox.top;
        } else {
            yEntry = staticBox.bottom - movingBox.top;
            yExit = staticBox.top - movingBox.bottom;
        }
        
        // Calculate time of entry and exit for each axis
        let xEntryTime, xExitTime, yEntryTime, yExitTime;
        
        if (velocity.x === 0) {
            xEntryTime = -Infinity;
            xExitTime = Infinity;
        } else {
            xEntryTime = xEntry / velocity.x;
            xExitTime = xExit / velocity.x;
        }
        
        if (velocity.y === 0) {
            yEntryTime = -Infinity;
            yExitTime = Infinity;
        } else {
            yEntryTime = yEntry / velocity.y;
            yExitTime = yExit / velocity.y;
        }
        
        // Find the latest entry time and earliest exit time
        const entryTime = Math.max(xEntryTime, yEntryTime);
        const exitTime = Math.min(xExitTime, yExitTime);
        
        // No collision if entry time is after exit time or entry time is negative
        if (entryTime > exitTime || entryTime < 0 || entryTime > 1) {
            return { hit: false, time: 1 };
        }
        
        // Calculate collision normal
        let normal = { x: 0, y: 0 };
        if (xEntryTime > yEntryTime) {
            normal.x = velocity.x < 0 ? 1 : -1;
        } else {
            normal.y = velocity.y < 0 ? 1 : -1;
        }
        
        // Calculate contact point
        const contact = {
            x: movingBox.x + velocity.x * entryTime,
            y: movingBox.y + velocity.y * entryTime
        };
        
        return {
            hit: true,
            time: entryTime,
            normal: normal,
            contact: contact
        };
    }
}
