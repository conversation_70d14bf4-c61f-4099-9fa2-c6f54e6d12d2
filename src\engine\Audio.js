/**
 * Audio system for sound effects and music
 */

export class Audio {
    constructor() {
        this.audioContext = null;
        this.sounds = new Map();
        this.music = new Map();
        this.isMuted = false;
        this.masterVolume = 1.0;
        this.soundVolume = 0.7;
        this.musicVolume = 0.5;
        this.currentMusic = null;
        this.initialized = false;
        
        // Sound definitions
        this.soundDefinitions = {
            jump: { frequency: 440, duration: 0.1, type: 'square' },
            coin: { frequency: 800, duration: 0.2, type: 'sine' },
            powerup: { frequency: 600, duration: 0.3, type: 'sawtooth' },
            stomp: { frequency: 200, duration: 0.15, type: 'square' },
            win: { frequency: 523, duration: 0.5, type: 'sine' },
            gameover: { frequency: 220, duration: 1.0, type: 'triangle' }
        };
    }
    
    /**
     * Initialize audio system
     */
    init() {
        if (this.initialized) return;
        
        try {
            // Create audio context
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // Generate procedural sounds
            this.generateSounds();
            
            // Handle audio context state
            if (this.audioContext.state === 'suspended') {
                // Audio context needs user interaction to start
                document.addEventListener('click', () => {
                    this.audioContext.resume();
                }, { once: true });
            }
            
            this.initialized = true;
            console.log('Audio system initialized');
            
        } catch (error) {
            console.warn('Audio not supported:', error);
            this.initialized = false;
        }
    }
    
    /**
     * Generate procedural sound effects
     */
    generateSounds() {
        Object.entries(this.soundDefinitions).forEach(([name, def]) => {
            this.sounds.set(name, this.generateSound(def));
        });
    }
    
    /**
     * Generate a single sound effect
     */
    generateSound(definition) {
        if (!this.audioContext) return null;
        
        const { frequency, duration, type } = definition;
        const sampleRate = this.audioContext.sampleRate;
        const length = sampleRate * duration;
        const buffer = this.audioContext.createBuffer(1, length, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < length; i++) {
            const t = i / sampleRate;
            let value = 0;
            
            switch (type) {
                case 'sine':
                    value = Math.sin(2 * Math.PI * frequency * t);
                    break;
                case 'square':
                    value = Math.sin(2 * Math.PI * frequency * t) > 0 ? 1 : -1;
                    break;
                case 'sawtooth':
                    value = 2 * (t * frequency - Math.floor(t * frequency + 0.5));
                    break;
                case 'triangle':
                    value = 2 * Math.abs(2 * (t * frequency - Math.floor(t * frequency + 0.5))) - 1;
                    break;
            }
            
            // Apply envelope (fade out)
            const envelope = 1 - (t / duration);
            data[i] = value * envelope * 0.3; // Reduce volume
        }
        
        return buffer;
    }
    
    /**
     * Play a sound effect
     */
    playSound(soundName, volume = 1.0, pitch = 1.0) {
        if (!this.initialized || this.isMuted || !this.audioContext) return;
        
        const soundBuffer = this.sounds.get(soundName);
        if (!soundBuffer) {
            console.warn(`Sound '${soundName}' not found`);
            return;
        }
        
        try {
            const source = this.audioContext.createBufferSource();
            const gainNode = this.audioContext.createGain();
            
            source.buffer = soundBuffer;
            source.playbackRate.value = pitch;
            
            gainNode.gain.value = volume * this.soundVolume * this.masterVolume;
            
            source.connect(gainNode);
            gainNode.connect(this.audioContext.destination);
            
            source.start();
            
        } catch (error) {
            console.warn('Error playing sound:', error);
        }
    }
    
    /**
     * Play jump sound with random pitch variation
     */
    playJumpSound() {
        const pitch = 0.9 + Math.random() * 0.2; // Random pitch between 0.9 and 1.1
        this.playSound('jump', 1.0, pitch);
    }
    
    /**
     * Play coin sound with ascending pitch
     */
    playCoinSound(coinCount = 1) {
        const pitch = 1.0 + (coinCount % 8) * 0.1; // Ascending pitch for multiple coins
        this.playSound('coin', 1.0, pitch);
    }
    
    /**
     * Load and play music (if audio files are available)
     */
    async loadMusic(name, url) {
        if (!this.initialized || !this.audioContext) return;
        
        try {
            const response = await fetch(url);
            const arrayBuffer = await response.arrayBuffer();
            const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
            
            this.music.set(name, audioBuffer);
            console.log(`Music '${name}' loaded`);
            
        } catch (error) {
            console.warn(`Failed to load music '${name}':`, error);
        }
    }
    
    /**
     * Play background music
     */
    playMusic(musicName, loop = true, volume = 1.0) {
        if (!this.initialized || this.isMuted || !this.audioContext) return;
        
        // Stop current music
        this.stopMusic();
        
        const musicBuffer = this.music.get(musicName);
        if (!musicBuffer) {
            console.warn(`Music '${musicName}' not found`);
            return;
        }
        
        try {
            const source = this.audioContext.createBufferSource();
            const gainNode = this.audioContext.createGain();
            
            source.buffer = musicBuffer;
            source.loop = loop;
            
            gainNode.gain.value = volume * this.musicVolume * this.masterVolume;
            
            source.connect(gainNode);
            gainNode.connect(this.audioContext.destination);
            
            source.start();
            
            this.currentMusic = {
                source: source,
                gainNode: gainNode
            };
            
        } catch (error) {
            console.warn('Error playing music:', error);
        }
    }
    
    /**
     * Stop current music
     */
    stopMusic() {
        if (this.currentMusic) {
            try {
                this.currentMusic.source.stop();
            } catch (error) {
                // Source might already be stopped
            }
            this.currentMusic = null;
        }
    }
    
    /**
     * Fade out current music
     */
    fadeOutMusic(duration = 1.0) {
        if (!this.currentMusic || !this.audioContext) return;
        
        const gainNode = this.currentMusic.gainNode;
        const currentTime = this.audioContext.currentTime;
        
        gainNode.gain.setValueAtTime(gainNode.gain.value, currentTime);
        gainNode.gain.linearRampToValueAtTime(0, currentTime + duration);
        
        setTimeout(() => {
            this.stopMusic();
        }, duration * 1000);
    }
    
    /**
     * Toggle mute state
     */
    toggleMute() {
        this.isMuted = !this.isMuted;
        
        if (this.isMuted) {
            this.stopMusic();
        }
        
        console.log(`Audio ${this.isMuted ? 'muted' : 'unmuted'}`);
    }
    
    /**
     * Set master volume
     */
    setMasterVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
        
        // Update current music volume
        if (this.currentMusic) {
            this.currentMusic.gainNode.gain.value = this.musicVolume * this.masterVolume;
        }
    }
    
    /**
     * Set sound effects volume
     */
    setSoundVolume(volume) {
        this.soundVolume = Math.max(0, Math.min(1, volume));
    }
    
    /**
     * Set music volume
     */
    setMusicVolume(volume) {
        this.musicVolume = Math.max(0, Math.min(1, volume));
        
        // Update current music volume
        if (this.currentMusic) {
            this.currentMusic.gainNode.gain.value = this.musicVolume * this.masterVolume;
        }
    }
    
    /**
     * Create a simple tone for testing
     */
    playTone(frequency, duration, type = 'sine', volume = 0.3) {
        if (!this.initialized || this.isMuted || !this.audioContext) return;
        
        try {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.type = type;
            oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
            
            gainNode.gain.setValueAtTime(volume * this.masterVolume, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);
            
            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);
            
            oscillator.start();
            oscillator.stop(this.audioContext.currentTime + duration);
            
        } catch (error) {
            console.warn('Error playing tone:', error);
        }
    }
    
    /**
     * Cleanup audio system
     */
    destroy() {
        this.stopMusic();
        
        if (this.audioContext) {
            this.audioContext.close();
        }
        
        this.sounds.clear();
        this.music.clear();
        this.initialized = false;
    }
}
