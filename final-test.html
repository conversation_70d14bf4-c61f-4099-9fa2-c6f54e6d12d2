<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Super Mario Platformer - Final Test</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas" width="1024" height="576"></canvas>
        
        <!-- UI Overlays -->
        <div id="startScreen" class="screen">
            <h1>Super Mario Platformer</h1>
            <button id="startButton">Start Game</button>
            <div class="controls">
                <p>Controls:</p>
                <p>A/D or ←/→ - Move</p>
                <p>Space/W/K - Jump</p>
                <p>P - Pause</p>
                <p>M - Mute</p>
            </div>
        </div>
        
        <div id="pauseScreen" class="screen hidden">
            <h2>Game Paused</h2>
            <button id="resumeButton">Resume</button>
            <button id="restartButton">Restart</button>
        </div>
        
        <div id="gameOverScreen" class="screen hidden">
            <h2>Game Over</h2>
            <button id="retryButton">Try Again</button>
            <button id="menuButton">Main Menu</button>
        </div>
        
        <div id="winScreen" class="screen hidden">
            <h2>You Win!</h2>
            <p id="finalScore">Score: 0</p>
            <button id="nextLevelButton">Next Level</button>
            <button id="menuButton2">Main Menu</button>
        </div>
        
        <!-- HUD -->
        <div id="hud" class="hidden">
            <div class="hud-item">Score: <span id="score">0</span></div>
            <div class="hud-item">Coins: <span id="coins">0</span></div>
            <div class="hud-item">Lives: <span id="lives">3</span></div>
            <div class="hud-item">Level: <span id="level">1</span></div>
            <div class="hud-item">
                <button id="muteButton">🔊</button>
            </div>
        </div>
        
        <!-- Debug Info -->
        <div style="position: absolute; top: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; z-index: 20;" id="debugInfo">
            Debug Info
        </div>
    </div>
    
    <!-- Load game scripts -->
    <script type="module">
        import { Game } from './src/engine/Game.js';
        import { Input } from './src/engine/Input.js';

        class GameManager {
            constructor() {
                this.canvas = document.getElementById('gameCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.game = null;
                this.currentLevel = 1;
                
                // UI elements
                this.startScreen = document.getElementById('startScreen');
                this.pauseScreen = document.getElementById('pauseScreen');
                this.gameOverScreen = document.getElementById('gameOverScreen');
                this.winScreen = document.getElementById('winScreen');
                this.hud = document.getElementById('hud');
                this.debugInfo = document.getElementById('debugInfo');
                
                // Initialize
                this.setupEventListeners();
                this.showStartScreen();
                
                // Debug update
                setInterval(() => this.updateDebugInfo(), 100);
            }
            
            updateDebugInfo() {
                if (!this.game || !this.game.player) {
                    this.debugInfo.innerHTML = 'Game not loaded';
                    return;
                }
                
                const player = this.game.player;
                this.debugInfo.innerHTML = `
                    Pos: (${player.x.toFixed(0)}, ${player.y.toFixed(0)})<br>
                    Vel: (${player.physics.velocity.x.toFixed(0)}, ${player.physics.velocity.y.toFixed(0)})<br>
                    Ground: ${player.physics.onGround}<br>
                    State: ${player.state}<br>
                    Anim: ${player.animationState}
                `;
            }
            
            setupEventListeners() {
                // Start screen
                document.getElementById('startButton').addEventListener('click', () => {
                    this.startGame();
                });
                
                // Pause screen
                document.getElementById('resumeButton').addEventListener('click', () => {
                    this.resumeGame();
                });
                
                document.getElementById('restartButton').addEventListener('click', () => {
                    this.restartGame();
                });
                
                // Game over screen
                document.getElementById('retryButton').addEventListener('click', () => {
                    this.restartGame();
                });
                
                document.getElementById('menuButton').addEventListener('click', () => {
                    this.showStartScreen();
                });
                
                // Win screen
                document.getElementById('nextLevelButton').addEventListener('click', () => {
                    this.nextLevel();
                });
                
                document.getElementById('menuButton2').addEventListener('click', () => {
                    this.showStartScreen();
                });
                
                // Mute button
                document.getElementById('muteButton').addEventListener('click', () => {
                    this.toggleMute();
                });
                
                // Keyboard shortcuts
                document.addEventListener('keydown', (e) => {
                    if (e.code === 'KeyP' && this.game && this.game.state === 'playing') {
                        this.pauseGame();
                    } else if (e.code === 'KeyM') {
                        this.toggleMute();
                    }
                });
            }
            
            async startGame() {
                this.hideAllScreens();
                this.showHUD();
                
                this.game = new Game(this.canvas, this.ctx);
                await this.game.loadLevel(this.currentLevel);
                this.game.start();
                
                // Setup game event listeners
                this.game.on('gameOver', () => this.showGameOverScreen());
                this.game.on('levelComplete', () => this.showWinScreen());
                this.game.on('scoreUpdate', (score) => this.updateScore(score));
                this.game.on('coinsUpdate', (coins) => this.updateCoins(coins));
                this.game.on('livesUpdate', (lives) => this.updateLives(lives));
            }
            
            pauseGame() {
                if (this.game) {
                    this.game.pause();
                    this.showPauseScreen();
                }
            }
            
            resumeGame() {
                if (this.game) {
                    this.game.resume();
                    this.hidePauseScreen();
                }
            }
            
            restartGame() {
                if (this.game) {
                    this.game.stop();
                }
                this.startGame();
            }
            
            nextLevel() {
                this.currentLevel++;
                if (this.currentLevel > 2) {
                    this.showStartScreen();
                    this.currentLevel = 1;
                } else {
                    this.startGame();
                }
            }
            
            toggleMute() {
                if (this.game && this.game.audio) {
                    this.game.audio.toggleMute();
                    const muteButton = document.getElementById('muteButton');
                    muteButton.textContent = this.game.audio.isMuted ? '🔇' : '🔊';
                }
            }
            
            // UI Management
            hideAllScreens() {
                this.startScreen.classList.add('hidden');
                this.pauseScreen.classList.add('hidden');
                this.gameOverScreen.classList.add('hidden');
                this.winScreen.classList.add('hidden');
            }
            
            showStartScreen() {
                this.hideAllScreens();
                this.hideHUD();
                this.startScreen.classList.remove('hidden');
                this.currentLevel = 1;
                if (this.game) {
                    this.game.stop();
                    this.game = null;
                }
            }
            
            showPauseScreen() {
                this.pauseScreen.classList.remove('hidden');
            }
            
            hidePauseScreen() {
                this.pauseScreen.classList.add('hidden');
            }
            
            showGameOverScreen() {
                this.hideHUD();
                this.gameOverScreen.classList.remove('hidden');
            }
            
            showWinScreen() {
                this.hideHUD();
                const finalScore = document.getElementById('finalScore');
                finalScore.textContent = `Score: ${this.game ? this.game.score : 0}`;
                this.winScreen.classList.remove('hidden');
                
                const nextButton = document.getElementById('nextLevelButton');
                if (this.currentLevel >= 2) {
                    nextButton.style.display = 'none';
                } else {
                    nextButton.style.display = 'block';
                }
            }
            
            showHUD() {
                this.hud.classList.remove('hidden');
                document.getElementById('level').textContent = this.currentLevel;
            }
            
            hideHUD() {
                this.hud.classList.add('hidden');
            }
            
            // HUD Updates
            updateScore(score) {
                document.getElementById('score').textContent = score;
            }
            
            updateCoins(coins) {
                document.getElementById('coins').textContent = coins;
            }
            
            updateLives(lives) {
                document.getElementById('lives').textContent = lives;
            }
        }

        // Initialize the game when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new GameManager();
        });
    </script>
</body>
</html>
