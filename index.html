<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Super Mario Platformer</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas" width="1024" height="576"></canvas>
        
        <!-- UI Overlays -->
        <div id="startScreen" class="screen">
            <h1>Super Mario Platformer</h1>
            <button id="startButton">Start Game</button>
            <div class="controls">
                <p>Controls:</p>
                <p>A/D or ←/→ - Move</p>
                <p>Space/W/K - Jump</p>
                <p>P - Pause</p>
                <p>M - Mute</p>
            </div>
        </div>
        
        <div id="pauseScreen" class="screen hidden">
            <h2>Game Paused</h2>
            <button id="resumeButton">Resume</button>
            <button id="restartButton">Restart</button>
        </div>
        
        <div id="gameOverScreen" class="screen hidden">
            <h2>Game Over</h2>
            <button id="retryButton">Try Again</button>
            <button id="menuButton">Main Menu</button>
        </div>
        
        <div id="winScreen" class="screen hidden">
            <h2>You Win!</h2>
            <p id="finalScore">Score: 0</p>
            <button id="nextLevelButton">Next Level</button>
            <button id="menuButton2">Main Menu</button>
        </div>
        
        <!-- HUD -->
        <div id="hud" class="hidden">
            <div class="hud-item">Score: <span id="score">0</span></div>
            <div class="hud-item">Coins: <span id="coins">0</span></div>
            <div class="hud-item">Lives: <span id="lives">3</span></div>
            <div class="hud-item">Level: <span id="level">1</span></div>
            <div class="hud-item">
                <button id="muteButton">🔊</button>
            </div>
        </div>
    </div>
    
    <!-- Load game scripts -->
    <script type="module" src="src/main.js"></script>
</body>
</html>
