/**
 * Spritesheet and animation utilities
 */

export class Spritesheet {
    constructor(imagePath, tileWidth, tileHeight) {
        this.image = new Image();
        this.tileWidth = tileWidth;
        this.tileHeight = tileHeight;
        this.loaded = false;
        
        this.image.onload = () => {
            this.loaded = true;
            this.cols = Math.floor(this.image.width / this.tileWidth);
            this.rows = Math.floor(this.image.height / this.tileHeight);
        };
        
        this.image.src = imagePath;
    }
    
    /**
     * Draw a specific tile from the spritesheet
     */
    drawTile(ctx, tileIndex, x, y, width = this.tileWidth, height = this.tileHeight) {
        if (!this.loaded) return;
        
        const col = tileIndex % this.cols;
        const row = Math.floor(tileIndex / this.cols);
        
        const sx = col * this.tileWidth;
        const sy = row * this.tileHeight;
        
        ctx.drawImage(
            this.image,
            sx, sy, this.tileWidth, this.tileHeight,
            x, y, width, height
        );
    }
    
    /**
     * Draw a tile by row and column
     */
    drawTileByCoords(ctx, col, row, x, y, width = this.tileWidth, height = this.tileHeight) {
        if (!this.loaded) return;
        
        const sx = col * this.tileWidth;
        const sy = row * this.tileHeight;
        
        ctx.drawImage(
            this.image,
            sx, sy, this.tileWidth, this.tileHeight,
            x, y, width, height
        );
    }
}

/**
 * Animation class for sprite animations
 */
export class Animation {
    constructor(spritesheet, frames, duration, loop = true) {
        this.spritesheet = spritesheet;
        this.frames = frames; // Array of tile indices
        this.duration = duration; // Duration in milliseconds
        this.loop = loop;
        this.currentFrame = 0;
        this.currentTime = 0;
        this.playing = false;
        this.finished = false;
        
        this.frameTime = this.duration / this.frames.length;
    }
    
    /**
     * Start the animation
     */
    play() {
        this.playing = true;
        this.finished = false;
        this.currentFrame = 0;
        this.currentTime = 0;
    }
    
    /**
     * Stop the animation
     */
    stop() {
        this.playing = false;
        this.currentFrame = 0;
        this.currentTime = 0;
        this.finished = false;
    }
    
    /**
     * Pause the animation
     */
    pause() {
        this.playing = false;
    }
    
    /**
     * Resume the animation
     */
    resume() {
        this.playing = true;
    }
    
    /**
     * Update the animation
     */
    update(dt) {
        if (!this.playing || this.finished) return;
        
        this.currentTime += dt;
        
        if (this.currentTime >= this.frameTime) {
            this.currentTime -= this.frameTime;
            this.currentFrame++;
            
            if (this.currentFrame >= this.frames.length) {
                if (this.loop) {
                    this.currentFrame = 0;
                } else {
                    this.currentFrame = this.frames.length - 1;
                    this.finished = true;
                    this.playing = false;
                }
            }
        }
    }
    
    /**
     * Draw the current frame
     */
    draw(ctx, x, y, width, height) {
        if (!this.spritesheet.loaded) return;
        
        const frameIndex = this.frames[this.currentFrame];
        this.spritesheet.drawTile(ctx, frameIndex, x, y, width, height);
    }
    
    /**
     * Get the current frame index
     */
    getCurrentFrame() {
        return this.frames[this.currentFrame];
    }
    
    /**
     * Check if animation is finished
     */
    isFinished() {
        return this.finished;
    }
    
    /**
     * Reset the animation to the beginning
     */
    reset() {
        this.currentFrame = 0;
        this.currentTime = 0;
        this.finished = false;
    }
}

/**
 * Sprite class that can hold multiple animations
 */
export class Sprite {
    constructor(spritesheet) {
        this.spritesheet = spritesheet;
        this.animations = new Map();
        this.currentAnimation = null;
        this.currentAnimationName = '';
    }
    
    /**
     * Add an animation to this sprite
     */
    addAnimation(name, frames, duration, loop = true) {
        const animation = new Animation(this.spritesheet, frames, duration, loop);
        this.animations.set(name, animation);
        
        // Set as current if it's the first animation
        if (this.animations.size === 1) {
            this.setAnimation(name);
        }
    }
    
    /**
     * Set the current animation
     */
    setAnimation(name, restart = false) {
        if (!this.animations.has(name)) {
            console.warn(`Animation '${name}' not found`);
            return;
        }
        
        if (this.currentAnimationName !== name || restart) {
            if (this.currentAnimation) {
                this.currentAnimation.stop();
            }
            
            this.currentAnimation = this.animations.get(name);
            this.currentAnimationName = name;
            this.currentAnimation.play();
        }
    }
    
    /**
     * Update the current animation
     */
    update(dt) {
        if (this.currentAnimation) {
            this.currentAnimation.update(dt);
        }
    }
    
    /**
     * Draw the current animation frame
     */
    draw(ctx, x, y, width, height) {
        if (this.currentAnimation) {
            this.currentAnimation.draw(ctx, x, y, width, height);
        }
    }
    
    /**
     * Check if current animation is finished
     */
    isAnimationFinished() {
        return this.currentAnimation ? this.currentAnimation.isFinished() : false;
    }
    
    /**
     * Get the current animation name
     */
    getCurrentAnimationName() {
        return this.currentAnimationName;
    }
}
