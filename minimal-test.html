<!DOCTYPE html>
<html>
<head>
    <title>Minimal Jump Test</title>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial; }
        canvas { border: 2px solid #000; background: #87CEEB; }
        .debug { background: #f0f0f0; padding: 10px; margin: 10px 0; font-family: monospace; }
    </style>
</head>
<body>
    <h1>Minimal Jump Test</h1>
    <p>Press SPACE to jump, A/D to move</p>
    <canvas id="canvas" width="800" height="400"></canvas>
    <div class="debug" id="debug">Debug info...</div>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        const debug = document.getElementById('debug');

        // Simple player
        const player = {
            x: 100,
            y: 350,
            width: 20,
            height: 20,
            vx: 0,
            vy: 0,
            onGround: false,
            jumpPower: 300,
            gravity: 800,
            speed: 200
        };

        // Ground
        const groundY = 370;

        // Input
        const keys = {};
        let jumpPressed = false;

        document.addEventListener('keydown', (e) => {
            keys[e.code] = true;
            if (e.code === 'Space') {
                jumpPressed = true;
                console.log('SPACE PRESSED!');
            }
            console.log('Key down:', e.code);
        });

        document.addEventListener('keyup', (e) => {
            keys[e.code] = false;
        });

        function update(dt) {
            // Reset jump pressed
            const wasJumpPressed = jumpPressed;
            jumpPressed = false;

            // Movement
            if (keys['KeyA']) player.vx = -player.speed;
            else if (keys['KeyD']) player.vx = player.speed;
            else player.vx *= 0.8;

            // Jump
            if (wasJumpPressed && player.onGround) {
                player.vy = -player.jumpPower;
                player.onGround = false;
                console.log('JUMPING! vy =', player.vy);
            }

            // Gravity
            if (!player.onGround) {
                player.vy += player.gravity * dt;
            }

            // Update position
            player.x += player.vx * dt;
            player.y += player.vy * dt;

            // Ground collision
            if (player.y + player.height >= groundY) {
                player.y = groundY - player.height;
                player.vy = 0;
                player.onGround = true;
            } else {
                player.onGround = false;
            }

            // Bounds
            if (player.x < 0) player.x = 0;
            if (player.x > canvas.width - player.width) player.x = canvas.width - player.width;

            // Debug
            debug.innerHTML = `
                Position: (${player.x.toFixed(1)}, ${player.y.toFixed(1)})<br>
                Velocity: (${player.vx.toFixed(1)}, ${player.vy.toFixed(1)})<br>
                On Ground: ${player.onGround}<br>
                Jump Pressed: ${wasJumpPressed}<br>
                Space Key: ${keys['Space'] || false}<br>
                Keys: ${Object.keys(keys).filter(k => keys[k]).join(', ')}
            `;
        }

        function render() {
            // Clear
            ctx.fillStyle = '#87CEEB';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Ground
            ctx.fillStyle = '#228B22';
            ctx.fillRect(0, groundY, canvas.width, canvas.height - groundY);

            // Player
            ctx.fillStyle = player.onGround ? '#0066FF' : '#FF6600';
            ctx.fillRect(player.x, player.y, player.width, player.height);

            // Player border
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 2;
            ctx.strokeRect(player.x, player.y, player.width, player.height);
        }

        let lastTime = 0;
        function gameLoop(time) {
            const dt = Math.min((time - lastTime) / 1000, 0.016);
            lastTime = time;

            update(dt);
            render();

            requestAnimationFrame(gameLoop);
        }

        console.log('Starting minimal test...');
        requestAnimationFrame(gameLoop);
    </script>
</body>
</html>
