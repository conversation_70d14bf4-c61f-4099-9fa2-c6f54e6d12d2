/**
 * Main Game class - handles game loop, state management, and coordination
 */

import { Input } from './Input.js';
import { Camera } from './Camera.js';
import { Physics } from './Physics.js';
import { Audio } from './Audio.js';
import { LevelLoader } from '../world/LevelLoader.js';
import { Player } from '../entities/Player.js';

export class Game {
    constructor(canvas, ctx) {
        this.canvas = canvas;
        this.ctx = ctx;
        
        // Game state
        this.state = 'loading'; // loading, playing, paused, gameOver, levelComplete
        this.running = false;
        this.lastTime = 0;
        this.deltaTime = 0;
        this.fixedTimeStep = 1000 / 60; // 60 FPS
        this.accumulator = 0;
        
        // Game systems
        this.input = new Input();
        this.camera = new Camera(canvas.width, canvas.height);
        this.physics = new Physics();
        this.audio = new Audio();
        this.levelLoader = new LevelLoader();
        
        // Game objects
        this.player = null;
        this.tilemap = null;
        this.entities = [];
        this.coins = [];
        this.enemies = [];
        this.powerUps = [];
        this.flag = null;
        
        // Game stats
        this.score = 0;
        this.coins_collected = 0;
        this.lives = 3;
        this.currentLevel = 1;
        
        // Event system
        this.eventListeners = new Map();
        
        // Initialize
        this.init();
    }
    
    init() {
        // Setup canvas
        this.ctx.imageSmoothingEnabled = false;
        
        // Setup input
        this.input.init();
        
        // Setup audio
        this.audio.init();
        
        console.log('Game initialized');
    }
    
    /**
     * Event system
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }
    
    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => callback(data));
        }
    }
    
    /**
     * Load a level
     */
    async loadLevel(levelNumber) {
        try {
            this.state = 'loading';
            this.currentLevel = levelNumber;
            
            // Load level data
            const levelData = await this.levelLoader.loadLevel(levelNumber);
            this.tilemap = levelData.tilemap;
            
            // Create player
            const playerSpawn = levelData.playerSpawn;
            this.player = new Player(playerSpawn.x, playerSpawn.y);
            
            // Create entities
            this.entities = [];
            this.coins = levelData.coins || [];
            this.enemies = levelData.enemies || [];
            this.powerUps = levelData.powerUps || [];
            this.flag = levelData.flag;
            
            // Combine all entities for easier management
            this.entities = [
                this.player,
                ...this.coins,
                ...this.enemies,
                ...this.powerUps
            ];
            
            if (this.flag) {
                this.entities.push(this.flag);
            }
            
            // Setup camera to follow player
            this.camera.setTarget(this.player);
            this.camera.setBounds(0, 0, this.tilemap.width * this.tilemap.tileSize, this.tilemap.height * this.tilemap.tileSize);
            
            this.state = 'playing';
            console.log(`Level ${levelNumber} loaded`);
            
        } catch (error) {
            console.error('Failed to load level:', error);
            this.state = 'gameOver';
        }
    }
    
    /**
     * Start the game loop
     */
    start() {
        if (this.running) return;
        
        this.running = true;
        this.lastTime = performance.now();
        this.gameLoop();
    }
    
    /**
     * Stop the game loop
     */
    stop() {
        this.running = false;
    }
    
    /**
     * Pause the game
     */
    pause() {
        this.state = 'paused';
    }
    
    /**
     * Resume the game
     */
    resume() {
        this.state = 'playing';
    }
    
    /**
     * Main game loop
     */
    gameLoop() {
        if (!this.running) return;
        
        const currentTime = performance.now();
        this.deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;
        
        // Cap delta time to prevent spiral of death
        this.deltaTime = Math.min(this.deltaTime, 100);
        
        // Fixed timestep for physics
        this.accumulator += this.deltaTime;
        
        while (this.accumulator >= this.fixedTimeStep) {
            this.update(this.fixedTimeStep / 1000); // Convert to seconds
            this.accumulator -= this.fixedTimeStep;
        }
        
        // Render with interpolation
        const alpha = this.accumulator / this.fixedTimeStep;
        this.render(alpha);
        
        requestAnimationFrame(() => this.gameLoop());
    }
    
    /**
     * Update game logic
     */
    update(dt) {
        if (this.state !== 'playing') return;
        
        // Update input
        this.input.update();
        
        // Update entities
        this.entities.forEach(entity => {
            if (entity && entity.update) {
                entity.update(dt, this);
            }
        });
        
        // Update physics
        this.physics.update(dt, this.entities, this.tilemap);
        
        // Update camera
        this.camera.update(dt);
        
        // Check game conditions
        this.checkGameConditions();
        
        // Clean up destroyed entities
        this.cleanupEntities();
    }
    
    /**
     * Render the game
     */
    render(alpha) {
        // Clear canvas
        this.ctx.fillStyle = '#5C94FC'; // Sky blue
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Save context
        this.ctx.save();
        
        // Apply camera transform
        this.camera.apply(this.ctx);
        
        // Render tilemap
        if (this.tilemap) {
            this.tilemap.render(this.ctx, this.camera);
        }
        
        // Render entities (sorted by depth)
        const sortedEntities = [...this.entities].sort((a, b) => {
            return (a.depth || 0) - (b.depth || 0);
        });
        
        sortedEntities.forEach(entity => {
            if (entity && entity.render && !entity.destroyed) {
                entity.render(this.ctx, alpha);
            }
        });
        
        // Restore context
        this.ctx.restore();
    }
    
    /**
     * Check win/lose conditions
     */
    checkGameConditions() {
        if (!this.player) return;
        
        // Check if player fell off the map
        if (this.player.y > this.tilemap.height * this.tilemap.tileSize + 100) {
            this.playerDied();
            return;
        }
        
        // Check if player reached the flag
        if (this.flag && !this.flag.destroyed) {
            const playerBounds = this.player.getBounds();
            const flagBounds = this.flag.getBounds();
            
            if (playerBounds.intersects(flagBounds)) {
                this.levelComplete();
                return;
            }
        }
    }
    
    /**
     * Handle player death
     */
    playerDied() {
        this.lives--;
        this.emit('livesUpdate', this.lives);
        
        if (this.lives <= 0) {
            this.gameOver();
        } else {
            // Respawn player
            this.respawnPlayer();
        }
    }
    
    /**
     * Respawn the player
     */
    respawnPlayer() {
        if (this.player) {
            this.player.respawn();
            this.camera.snapToTarget();
        }
    }
    
    /**
     * Handle level completion
     */
    levelComplete() {
        this.state = 'levelComplete';
        this.audio.playSound('win');
        this.emit('levelComplete');
    }
    
    /**
     * Handle game over
     */
    gameOver() {
        this.state = 'gameOver';
        this.audio.playSound('gameover');
        this.emit('gameOver');
    }
    
    /**
     * Add score
     */
    addScore(points) {
        this.score += points;
        this.emit('scoreUpdate', this.score);
    }
    
    /**
     * Collect coin
     */
    collectCoin() {
        this.coins_collected++;
        this.addScore(100);
        this.audio.playSound('coin');
        this.emit('coinsUpdate', this.coins_collected);
    }
    
    /**
     * Collect power-up
     */
    collectPowerUp() {
        this.addScore(1000);
        this.audio.playSound('powerup');
    }
    
    /**
     * Clean up destroyed entities
     */
    cleanupEntities() {
        this.entities = this.entities.filter(entity => !entity.destroyed);
        this.coins = this.coins.filter(coin => !coin.destroyed);
        this.enemies = this.enemies.filter(enemy => !enemy.destroyed);
        this.powerUps = this.powerUps.filter(powerUp => !powerUp.destroyed);
    }
    
    /**
     * Get entities in a specific area
     */
    getEntitiesInArea(bounds) {
        return this.entities.filter(entity => {
            if (entity.destroyed) return false;
            const entityBounds = entity.getBounds();
            return entityBounds && bounds.intersects(entityBounds);
        });
    }
}
