<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Super Mario Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        #gameCanvas {
            border: 2px solid #333;
            background: #5C94FC;
            image-rendering: pixelated;
        }
        .info {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Super Mario Platformer - Test</h1>
    <div class="info">
        <p>Use WASD or Arrow Keys to move, Space to jump</p>
        <p>P to pause, M to mute</p>
    </div>
    <canvas id="gameCanvas" width="1024" height="576"></canvas>
    
    <script type="module">
        console.log('Starting game test...');
        
        try {
            // Test basic imports
            import('./src/utils/Math.js').then(module => {
                console.log('Math utils loaded:', module);
            });
            
            import('./src/utils/AABB.js').then(module => {
                console.log('AABB loaded:', module);
            });
            
            import('./src/engine/Input.js').then(module => {
                console.log('Input loaded:', module);
            });
            
            import('./src/engine/Game.js').then(module => {
                console.log('Game loaded:', module);
                
                // Try to create game instance
                const canvas = document.getElementById('gameCanvas');
                const ctx = canvas.getContext('2d');
                const game = new module.Game(canvas, ctx);
                
                console.log('Game instance created:', game);
                
                // Try to load level 1
                game.loadLevel(1).then(() => {
                    console.log('Level 1 loaded successfully');
                    game.start();
                    console.log('Game started');
                }).catch(error => {
                    console.error('Failed to load level:', error);
                });
                
            }).catch(error => {
                console.error('Failed to load Game:', error);
            });
            
        } catch (error) {
            console.error('Error in test:', error);
        }
    </script>
</body>
</html>
