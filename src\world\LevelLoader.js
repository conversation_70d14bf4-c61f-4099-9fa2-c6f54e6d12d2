/**
 * Level loader for loading level data and creating game objects
 */

import { Tilemap } from './Tilemap.js';
import { Coin } from '../entities/Coin.js';
import { Enemy } from '../entities/Enemy.js';
import { PowerUp } from '../entities/PowerUp.js';
import { Flag } from '../entities/Flag.js';

export class LevelLoader {
    constructor() {
        this.levels = new Map();
        this.loadedLevels = new Map();
    }
    
    /**
     * Load a level by number
     */
    async loadLevel(levelNumber) {
        // Check if level is already loaded
        if (this.loadedLevels.has(levelNumber)) {
            return this.loadedLevels.get(levelNumber);
        }
        
        try {
            // Try to load from JSON file first
            const levelData = await this.loadLevelFromFile(levelNumber);
            const processedLevel = this.processLevelData(levelData);
            
            this.loadedLevels.set(levelNumber, processedLevel);
            return processedLevel;
            
        } catch (error) {
            console.warn(`Failed to load level ${levelNumber} from file, generating default:`, error);
            
            // Generate default level if file loading fails
            const defaultLevel = this.generateDefaultLevel(levelNumber);
            this.loadedLevels.set(levelNumber, defaultLevel);
            return defaultLevel;
        }
    }
    
    /**
     * Load level data from JSON file
     */
    async loadLevelFromFile(levelNumber) {
        const response = await fetch(`src/world/levels/level${levelNumber}.json`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        return await response.json();
    }
    
    /**
     * Process raw level data into game objects
     */
    processLevelData(levelData) {
        // Create tilemap
        const tilemap = new Tilemap(levelData.width, levelData.height, levelData.tileSize || 32);
        tilemap.loadFromArray(levelData.tiles);
        
        // Find spawn points from tilemap
        const spawnPoints = tilemap.findSpawnPoints();
        
        // Override with explicit spawn points if provided
        const playerSpawn = levelData.playerSpawn || spawnPoints.player || { x: 64, y: 480 };
        
        // Create entities
        const coins = this.createCoins(levelData.coins || spawnPoints.coins);
        const enemies = this.createEnemies(levelData.enemies || spawnPoints.enemies);
        const powerUps = this.createPowerUps(levelData.powerUps || spawnPoints.powerUps);
        const flag = this.createFlag(levelData.flag || spawnPoints.flag);
        
        return {
            tilemap,
            playerSpawn,
            coins,
            enemies,
            powerUps,
            flag,
            metadata: levelData.metadata || {}
        };
    }
    
    /**
     * Create coin entities from spawn data
     */
    createCoins(coinData) {
        return coinData.map(data => new Coin(data.x, data.y));
    }
    
    /**
     * Create enemy entities from spawn data
     */
    createEnemies(enemyData) {
        return enemyData.map(data => {
            const enemy = new Enemy(data.x, data.y);
            if (data.type) enemy.setType(data.type);
            if (data.direction) enemy.setDirection(data.direction);
            return enemy;
        });
    }
    
    /**
     * Create power-up entities from spawn data
     */
    createPowerUps(powerUpData) {
        return powerUpData.map(data => {
            const powerUp = new PowerUp(data.x, data.y);
            if (data.type) powerUp.setType(data.type);
            return powerUp;
        });
    }
    
    /**
     * Create flag entity from spawn data
     */
    createFlag(flagData) {
        if (!flagData) return null;
        return new Flag(flagData.x, flagData.y);
    }
    
    /**
     * Generate a default level when file loading fails
     */
    generateDefaultLevel(levelNumber) {
        const width = levelNumber === 1 ? 200 : 250;
        const height = 20;
        const tileSize = 32;
        
        const tilemap = new Tilemap(width, height, tileSize);
        
        if (levelNumber === 1) {
            this.generateLevel1(tilemap);
        } else {
            this.generateLevel2(tilemap);
        }
        
        // Find spawn points
        const spawnPoints = tilemap.findSpawnPoints();
        
        // Create default entities if no spawn points found
        const playerSpawn = spawnPoints.player || { x: 64, y: 480 };
        const coins = spawnPoints.coins.length > 0 ? this.createCoins(spawnPoints.coins) : this.generateDefaultCoins(levelNumber);
        const enemies = spawnPoints.enemies.length > 0 ? this.createEnemies(spawnPoints.enemies) : this.generateDefaultEnemies(levelNumber);
        const powerUps = spawnPoints.powerUps.length > 0 ? this.createPowerUps(spawnPoints.powerUps) : this.generateDefaultPowerUps(levelNumber);
        const flag = spawnPoints.flag ? this.createFlag(spawnPoints.flag) : new Flag((width - 5) * tileSize, (height - 6) * tileSize);
        
        return {
            tilemap,
            playerSpawn,
            coins,
            enemies,
            powerUps,
            flag,
            metadata: { name: `Level ${levelNumber}`, generated: true }
        };
    }
    
    /**
     * Generate Level 1 layout
     */
    generateLevel1(tilemap) {
        const width = tilemap.width;
        const height = tilemap.height;
        
        // Fill with air
        tilemap.tiles.fill(0);
        
        // Ground layer
        for (let x = 0; x < width; x++) {
            for (let y = height - 3; y < height; y++) {
                tilemap.setTile(x, y, 1); // Ground
            }
        }
        
        // Platforms and obstacles
        this.addPlatforms(tilemap, [
            { x: 15, y: height - 8, width: 5, type: 2 }, // Brick platform
            { x: 30, y: height - 6, width: 4, type: 2 },
            { x: 50, y: height - 10, width: 3, type: 2 },
            { x: 70, y: height - 7, width: 6, type: 2 },
            { x: 90, y: height - 9, width: 4, type: 2 },
            { x: 120, y: height - 8, width: 5, type: 2 },
            { x: 150, y: height - 6, width: 4, type: 2 }
        ]);
        
        // Pipes
        this.addPipe(tilemap, 40, height - 6, 4);
        this.addPipe(tilemap, 100, height - 8, 5);
        
        // Gaps (remove ground)
        this.addGap(tilemap, 25, 3);
        this.addGap(tilemap, 60, 4);
        this.addGap(tilemap, 110, 5);
        this.addGap(tilemap, 140, 3);
        
        // Entity spawn points
        this.addEntitySpawns(tilemap, {
            coins: [
                { x: 20, y: height - 9 }, { x: 35, y: height - 7 }, { x: 55, y: height - 11 },
                { x: 75, y: height - 8 }, { x: 95, y: height - 10 }, { x: 125, y: height - 9 },
                { x: 155, y: height - 7 }, { x: 175, y: height - 4 }
            ],
            enemies: [
                { x: 80, y: height - 4 }, { x: 130, y: height - 4 }, { x: 160, y: height - 4 }
            ],
            powerUps: [
                { x: 52, y: height - 11 }
            ]
        });
    }
    
    /**
     * Generate Level 2 layout (harder)
     */
    generateLevel2(tilemap) {
        const width = tilemap.width;
        const height = tilemap.height;

        // Fill with air
        tilemap.tiles.fill(0);

        // Ground layer (more gaps)
        for (let x = 0; x < width; x++) {
            for (let y = height - 3; y < height; y++) {
                tilemap.setTile(x, y, 1);
            }
        }

        // More complex platforms
        this.addPlatforms(tilemap, [
            { x: 10, y: height - 6, width: 3, type: 2 },
            { x: 20, y: height - 9, width: 2, type: 2 },
            { x: 30, y: height - 12, width: 4, type: 2 },
            { x: 45, y: height - 8, width: 3, type: 2 },
            { x: 60, y: height - 15, width: 5, type: 2 },
            { x: 80, y: height - 10, width: 4, type: 2 },
            { x: 100, y: height - 13, width: 3, type: 2 },
            { x: 120, y: height - 7, width: 6, type: 2 },
            { x: 140, y: height - 11, width: 4, type: 2 },
            { x: 170, y: height - 9, width: 5, type: 2 },
            { x: 200, y: height - 6, width: 4, type: 2 }
        ]);

        // More pipes
        this.addPipe(tilemap, 35, height - 6, 3);
        this.addPipe(tilemap, 70, height - 8, 5);
        this.addPipe(tilemap, 110, height - 6, 3);
        this.addPipe(tilemap, 180, height - 7, 4);

        // Larger gaps
        this.addGap(tilemap, 15, 4);
        this.addGap(tilemap, 40, 6);
        this.addGap(tilemap, 75, 5);
        this.addGap(tilemap, 105, 7);
        this.addGap(tilemap, 135, 4);
        this.addGap(tilemap, 165, 6);
        this.addGap(tilemap, 195, 4);

        // More entity spawns
        this.addEntitySpawns(tilemap, {
            coins: [
                { x: 12, y: height - 7 }, { x: 22, y: height - 10 }, { x: 32, y: height - 13 },
                { x: 47, y: height - 9 }, { x: 62, y: height - 16 }, { x: 82, y: height - 11 },
                { x: 102, y: height - 14 }, { x: 122, y: height - 8 }, { x: 142, y: height - 12 },
                { x: 172, y: height - 10 }, { x: 202, y: height - 7 }, { x: 220, y: height - 4 }
            ],
            enemies: [
                { x: 50, y: height - 4 }, { x: 90, y: height - 4 }, { x: 130, y: height - 4 },
                { x: 160, y: height - 4 }, { x: 190, y: height - 4 }, { x: 210, y: height - 4 }
            ],
            powerUps: [
                { x: 32, y: height - 13 }, { x: 142, y: height - 12 }
            ]
        });

        // Add flag at the end
        tilemap.setTile(width - 5, height - 6, 14);
    }
    
    /**
     * Helper methods for level generation
     */
    addPlatforms(tilemap, platforms) {
        platforms.forEach(platform => {
            for (let x = platform.x; x < platform.x + platform.width; x++) {
                tilemap.setTile(x, platform.y, platform.type);
            }
        });
    }
    
    addPipe(tilemap, x, startY, height) {
        for (let y = startY; y < startY + height; y++) {
            tilemap.setTile(x, y, 3);
            tilemap.setTile(x + 1, y, 3);
        }
    }
    
    addGap(tilemap, startX, width) {
        const height = tilemap.height;
        for (let x = startX; x < startX + width; x++) {
            for (let y = height - 3; y < height; y++) {
                tilemap.setTile(x, y, 0); // Remove ground
            }
        }
    }
    
    addEntitySpawns(tilemap, spawns) {
        spawns.coins?.forEach(spawn => tilemap.setTile(spawn.x, spawn.y, 12));
        spawns.enemies?.forEach(spawn => tilemap.setTile(spawn.x, spawn.y, 11));
        spawns.powerUps?.forEach(spawn => tilemap.setTile(spawn.x, spawn.y, 13));
    }
    
    generateDefaultCoins(levelNumber) {
        const coins = [];
        const count = levelNumber === 1 ? 8 : 12;
        for (let i = 0; i < count; i++) {
            coins.push(new Coin(100 + i * 150, 300));
        }
        return coins;
    }
    
    generateDefaultEnemies(levelNumber) {
        const enemies = [];
        const count = levelNumber === 1 ? 3 : 6;
        for (let i = 0; i < count; i++) {
            enemies.push(new Enemy(200 + i * 200, 450));
        }
        return enemies;
    }
    
    generateDefaultPowerUps(levelNumber) {
        const powerUps = [];
        powerUps.push(new PowerUp(400, 300));
        if (levelNumber === 2) {
            powerUps.push(new PowerUp(800, 250));
        }
        return powerUps;
    }
}
