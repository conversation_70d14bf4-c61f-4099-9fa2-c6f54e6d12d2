<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Super Mario Debug</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        #gameCanvas {
            border: 2px solid #333;
            background: #5C94FC;
            image-rendering: pixelated;
        }
        .debug {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            font-family: monospace;
        }
        .controls {
            background: #e0e0e0;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Super Mario Platformer - Debug Mode</h1>
    
    <div class="controls">
        <strong>Controls:</strong> WASD or Arrow Keys to move, Space/W/K to jump, P to pause, M to mute
    </div>
    
    <canvas id="gameCanvas" width="1024" height="576"></canvas>
    
    <div class="debug">
        <div id="debugInfo">Loading...</div>
    </div>
    
    <script type="module">
        console.log('Starting debug mode...');
        
        let game = null;
        let debugInfo = document.getElementById('debugInfo');
        
        function updateDebugInfo() {
            if (!game || !game.player) {
                debugInfo.innerHTML = 'Game not loaded yet...';
                return;
            }
            
            const player = game.player;
            const input = game.input;
            
            const info = `
                <strong>Player State:</strong><br>
                Position: (${player.x.toFixed(1)}, ${player.y.toFixed(1)})<br>
                Velocity: (${player.physics.velocity.x.toFixed(1)}, ${player.physics.velocity.y.toFixed(1)})<br>
                On Ground: ${player.physics.onGround}<br>
                On Wall: ${player.physics.onWall}<br>
                Coyote Time: ${player.physics.coyoteTime.toFixed(3)}<br>
                Jump Buffer: ${player.physics.jumpBuffer.toFixed(3)}<br>
                State: ${player.state}<br>
                Animation: ${player.animationState}<br>
                Facing: ${player.facing}<br>
                <br>
                <strong>Input State:</strong><br>
                Jump Pressed: ${input.isActionPressed('jump')}<br>
                Jump Held: ${input.isActionActive('jump')}<br>
                Left: ${input.isActionActive('left')}<br>
                Right: ${input.isActionActive('right')}<br>
                <br>
                <strong>Game State:</strong><br>
                Game State: ${game.state}<br>
                Score: ${game.score}<br>
                Lives: ${game.lives}<br>
                Level: ${game.currentLevel}<br>
                Entities: ${game.entities.length}<br>
            `;
            
            debugInfo.innerHTML = info;
        }
        
        try {
            const { Game } = await import('./src/engine/Game.js');
            
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            game = new Game(canvas, ctx);
            
            console.log('Game instance created');
            
            // Load level 1
            await game.loadLevel(1);
            console.log('Level loaded');
            
            // Start game
            game.start();
            console.log('Game started');
            
            // Debug info update loop
            setInterval(updateDebugInfo, 100);
            
            // Add keyboard event listener for debugging
            document.addEventListener('keydown', (e) => {
                console.log('Key pressed:', e.code, 'Jump action:', game.input.isActionPressed('jump'));
            });
            
        } catch (error) {
            console.error('Error:', error);
            debugInfo.innerHTML = `Error: ${error.message}`;
        }
    </script>
</body>
</html>
