/**
 * Input handling system for keyboard and touch controls
 */

export class Input {
    constructor() {
        // Keyboard state
        this.keys = new Map();
        this.keysPressed = new Map();
        this.keysReleased = new Map();
        
        // Touch state for mobile
        this.touches = new Map();
        this.touchButtons = new Map();
        
        // Input mapping
        this.inputMap = {
            // Movement
            left: ['ArrowLeft', 'KeyA'],
            right: ['ArrowRight', 'KeyD'],
            jump: ['Space', 'KeyW', 'KeyK'],
            
            // Game controls
            pause: ['KeyP'],
            mute: ['KeyM'],
            
            // Debug
            debug: ['KeyF1']
        };
        
        // Virtual buttons for mobile
        this.virtualButtons = [];
        this.showVirtualButtons = false;
        
        this.initialized = false;
    }
    
    /**
     * Initialize input system
     */
    init() {
        if (this.initialized) return;
        
        this.setupKeyboardEvents();
        this.setupTouchEvents();
        this.createVirtualButtons();
        this.detectMobile();
        
        this.initialized = true;
        console.log('Input system initialized');
    }
    
    /**
     * Setup keyboard event listeners
     */
    setupKeyboardEvents() {
        document.addEventListener('keydown', (e) => {
            this.handleKeyDown(e);
        });
        
        document.addEventListener('keyup', (e) => {
            this.handleKeyUp(e);
        });
        
        // Prevent default behavior for game keys
        document.addEventListener('keydown', (e) => {
            const gameKeys = Object.values(this.inputMap).flat();
            if (gameKeys.includes(e.code)) {
                e.preventDefault();
            }
        });
    }
    
    /**
     * Setup touch event listeners for mobile
     */
    setupTouchEvents() {
        document.addEventListener('touchstart', (e) => {
            this.handleTouchStart(e);
        });
        
        document.addEventListener('touchend', (e) => {
            this.handleTouchEnd(e);
        });
        
        document.addEventListener('touchmove', (e) => {
            e.preventDefault(); // Prevent scrolling
        }, { passive: false });
    }
    
    /**
     * Handle key down events
     */
    handleKeyDown(e) {
        const code = e.code;
        
        if (!this.keys.get(code)) {
            this.keysPressed.set(code, true);
        }
        
        this.keys.set(code, true);
    }
    
    /**
     * Handle key up events
     */
    handleKeyUp(e) {
        const code = e.code;
        this.keys.set(code, false);
        this.keysReleased.set(code, true);
    }
    
    /**
     * Handle touch start events
     */
    handleTouchStart(e) {
        e.preventDefault();
        
        for (let touch of e.changedTouches) {
            this.touches.set(touch.identifier, {
                x: touch.clientX,
                y: touch.clientY,
                startX: touch.clientX,
                startY: touch.clientY
            });
            
            // Check virtual button touches
            this.checkVirtualButtonTouch(touch.clientX, touch.clientY, true);
        }
    }
    
    /**
     * Handle touch end events
     */
    handleTouchEnd(e) {
        e.preventDefault();
        
        for (let touch of e.changedTouches) {
            this.touches.delete(touch.identifier);
            
            // Release virtual buttons
            this.checkVirtualButtonTouch(touch.clientX, touch.clientY, false);
        }
    }
    
    /**
     * Check if touch hits a virtual button
     */
    checkVirtualButtonTouch(x, y, pressed) {
        this.virtualButtons.forEach(button => {
            if (x >= button.x && x <= button.x + button.width &&
                y >= button.y && y <= button.y + button.height) {
                
                if (pressed && !this.touchButtons.get(button.action)) {
                    this.keysPressed.set(button.action, true);
                }
                
                this.touchButtons.set(button.action, pressed);
                
                if (!pressed) {
                    this.keysReleased.set(button.action, true);
                }
            }
        });
    }
    
    /**
     * Create virtual buttons for mobile
     */
    createVirtualButtons() {
        const buttonSize = 60;
        const margin = 20;
        const bottomMargin = 100;
        
        // Left button
        this.virtualButtons.push({
            action: 'ArrowLeft',
            x: margin,
            y: window.innerHeight - bottomMargin - buttonSize,
            width: buttonSize,
            height: buttonSize,
            label: '←'
        });
        
        // Right button
        this.virtualButtons.push({
            action: 'ArrowRight',
            x: margin + buttonSize + 10,
            y: window.innerHeight - bottomMargin - buttonSize,
            width: buttonSize,
            height: buttonSize,
            label: '→'
        });
        
        // Jump button
        this.virtualButtons.push({
            action: 'Space',
            x: window.innerWidth - margin - buttonSize,
            y: window.innerHeight - bottomMargin - buttonSize,
            width: buttonSize,
            height: buttonSize,
            label: 'A'
        });
    }
    
    /**
     * Detect if device is mobile
     */
    detectMobile() {
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        const isTouchDevice = 'ontouchstart' in window;
        
        this.showVirtualButtons = isMobile || isTouchDevice;
        
        if (this.showVirtualButtons) {
            this.renderVirtualButtons();
        }
    }
    
    /**
     * Render virtual buttons on screen
     */
    renderVirtualButtons() {
        // Create virtual button container
        const container = document.createElement('div');
        container.id = 'virtualButtons';
        container.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1000;
        `;
        
        this.virtualButtons.forEach(button => {
            const buttonElement = document.createElement('div');
            buttonElement.style.cssText = `
                position: absolute;
                left: ${button.x}px;
                top: ${button.y}px;
                width: ${button.width}px;
                height: ${button.height}px;
                background: rgba(255, 255, 255, 0.3);
                border: 2px solid rgba(255, 255, 255, 0.5);
                border-radius: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24px;
                font-weight: bold;
                color: white;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                pointer-events: auto;
                user-select: none;
                touch-action: manipulation;
            `;
            buttonElement.textContent = button.label;
            container.appendChild(buttonElement);
        });
        
        document.body.appendChild(container);
    }
    
    /**
     * Update input state (call once per frame)
     */
    update() {
        // Clear frame-specific states
        this.keysPressed.clear();
        this.keysReleased.clear();
    }
    
    /**
     * Check if an action is currently active
     */
    isActionActive(action) {
        const keys = this.inputMap[action] || [action];
        
        // Check keyboard
        for (let key of keys) {
            if (this.keys.get(key)) {
                return true;
            }
        }
        
        // Check touch buttons
        for (let key of keys) {
            if (this.touchButtons.get(key)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check if an action was just pressed this frame
     */
    isActionPressed(action) {
        const keys = this.inputMap[action] || [action];
        
        for (let key of keys) {
            if (this.keysPressed.get(key)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check if an action was just released this frame
     */
    isActionReleased(action) {
        const keys = this.inputMap[action] || [action];
        
        for (let key of keys) {
            if (this.keysReleased.get(key)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get movement input as a normalized vector
     */
    getMovementVector() {
        let x = 0;
        let y = 0;
        
        if (this.isActionActive('left')) x -= 1;
        if (this.isActionActive('right')) x += 1;
        
        return { x, y };
    }
    
    /**
     * Check if jump was pressed
     */
    isJumpPressed() {
        return this.isActionPressed('jump');
    }
    
    /**
     * Check if jump is being held
     */
    isJumpHeld() {
        return this.isActionActive('jump');
    }
    
    /**
     * Cleanup input system
     */
    destroy() {
        // Remove virtual buttons
        const virtualButtons = document.getElementById('virtualButtons');
        if (virtualButtons) {
            virtualButtons.remove();
        }
        
        this.initialized = false;
    }
}
