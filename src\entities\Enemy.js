/**
 * Enemy entity with AI and collision detection
 */

import { AABB } from '../utils/AABB.js';
import { MathUtils } from '../utils/Math.js';

export class Enemy {
    constructor(x, y) {
        // Position and size
        this.x = x;
        this.y = y;
        this.width = 16;
        this.height = 16;
        
        // Physics
        this.physics = {
            velocity: { x: -50, y: 0 }, // Start moving left
            useGravity: true,
            onGround: false,
            onWall: false,
            onCeiling: false,
            solid: false,
            collidesWithTiles: true,
            collidesWithEntities: false
        };
        
        // Enemy properties
        this.type = 'enemy';
        this.enemyType = 'goomba';
        this.direction = -1; // -1 = left, 1 = right
        this.moveSpeed = 50;
        this.state = 'walking'; // walking, squashed, dead
        
        // AI properties
        this.turnOnWall = true;
        this.turnOnEdge = true;
        this.edgeCheckDistance = 20;
        
        // Animation
        this.animationTime = 0;
        this.animationFrame = 0;
        
        // Squash effect
        this.squashTime = 0;
        this.maxSquashTime = 0.5;
        
        // Entity properties
        this.destroyed = false;
        this.depth = 5;
        
        // Game reference
        this.game = null;
    }
    
    /**
     * Set enemy type
     */
    setType(type) {
        this.enemyType = type;
        
        switch (type) {
            case 'goomba':
                this.moveSpeed = 50;
                this.width = 16;
                this.height = 16;
                break;
            case 'koopa':
                this.moveSpeed = 40;
                this.width = 16;
                this.height = 24;
                break;
        }
    }
    
    /**
     * Set movement direction
     */
    setDirection(direction) {
        this.direction = direction;
        this.physics.velocity.x = this.direction * this.moveSpeed;
    }
    
    /**
     * Update enemy logic
     */
    update(dt, game) {
        this.game = game;
        
        if (this.state === 'squashed') {
            this.updateSquashed(dt);
            return;
        }
        
        if (this.state === 'dead') {
            return;
        }
        
        // Update AI
        this.updateAI(dt, game);
        
        // Update animation
        this.updateAnimation(dt);
        
        // Update physics state
        this.updatePhysicsState(dt);
    }
    
    /**
     * Update AI behavior
     */
    updateAI(dt, game) {
        // Check for walls and edges
        if (this.turnOnWall && this.physics.onWall) {
            this.turnAround();
        }
        
        if (this.turnOnEdge && this.physics.onGround) {
            this.checkForEdge(game.tilemap);
        }
        
        // Maintain movement speed
        this.physics.velocity.x = this.direction * this.moveSpeed;
    }
    
    /**
     * Check if enemy is about to walk off an edge
     */
    checkForEdge(tilemap) {
        if (!tilemap) return;
        
        const checkX = this.direction > 0 ? 
            this.x + this.width + this.edgeCheckDistance : 
            this.x - this.edgeCheckDistance;
        const checkY = this.y + this.height + 5; // Check slightly below
        
        if (!tilemap.isSolid(checkX, checkY)) {
            this.turnAround();
        }
    }
    
    /**
     * Turn around
     */
    turnAround() {
        this.direction *= -1;
        this.physics.velocity.x = this.direction * this.moveSpeed;
    }
    
    /**
     * Update animation
     */
    updateAnimation(dt) {
        this.animationTime += dt;
        
        // Simple walking animation
        const frameTime = 0.5;
        if (this.animationTime >= frameTime) {
            this.animationFrame = (this.animationFrame + 1) % 2;
            this.animationTime = 0;
        }
    }
    
    /**
     * Update physics state
     */
    updatePhysicsState(dt) {
        // Update bounds for collision detection
        this.updateBounds();
    }
    
    /**
     * Update squashed state
     */
    updateSquashed(dt) {
        this.squashTime += dt;
        
        if (this.squashTime >= this.maxSquashTime) {
            this.destroyed = true;
        }
    }
    
    /**
     * Handle collision with other entities
     */
    onCollision(other, axis) {
        if (other.type === 'player') {
            // Collision is handled by player
            return;
        }
        
        if (other.type === 'enemy' && axis === 'horizontal') {
            // Enemies bounce off each other
            this.turnAround();
        }
    }
    
    /**
     * Called when physics detects collision with tiles
     */
    onPhysicsUpdate(dt, oldX, oldY) {
        // Check if enemy hit a wall
        if (this.physics.onWall && this.turnOnWall) {
            this.turnAround();
        }
    }
    
    /**
     * Get squashed by player
     */
    getSquashed() {
        if (this.state === 'squashed' || this.state === 'dead') return;

        this.state = 'squashed';
        this.squashTime = 0;
        this.physics.velocity.x = 0;
        this.physics.velocity.y = 0;
        this.physics.useGravity = false;
        this.height = 8; // Flatten the enemy
        this.destroyed = true; // Mark for removal
    }
    
    /**
     * Kill enemy (for other types of damage)
     */
    kill() {
        this.state = 'dead';
        this.destroyed = true;
    }
    
    /**
     * Get collision bounds
     */
    getBounds() {
        return new AABB(this.x, this.y, this.width, this.height);
    }
    
    /**
     * Update collision bounds
     */
    updateBounds() {
        // Bounds are calculated dynamically in getBounds()
    }
    
    /**
     * Render enemy
     */
    render(ctx, alpha) {
        if (this.state === 'dead') return;
        
        // Calculate render position
        const renderX = this.x;
        const renderY = this.y;
        
        // Choose color based on enemy type and state
        let color = '#8B4513'; // Brown for goomba
        
        if (this.enemyType === 'koopa') {
            color = '#228B22'; // Green for koopa
        }
        
        if (this.state === 'squashed') {
            color = '#654321'; // Darker when squashed
        }
        
        // Draw enemy body
        ctx.fillStyle = color;
        ctx.fillRect(renderX, renderY, this.width, this.height);
        
        // Draw border
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 1;
        ctx.strokeRect(renderX, renderY, this.width, this.height);
        
        if (this.state !== 'squashed') {
            // Draw eyes
            ctx.fillStyle = '#FFFFFF';
            const eyeSize = 2;
            const eyeY = renderY + 3;
            
            ctx.fillRect(renderX + 3, eyeY, eyeSize, eyeSize);
            ctx.fillRect(renderX + this.width - 5, eyeY, eyeSize, eyeSize);
            
            // Draw pupils
            ctx.fillStyle = '#000000';
            const pupilSize = 1;
            const pupilOffset = this.direction > 0 ? 1 : 0;
            
            ctx.fillRect(renderX + 3 + pupilOffset, eyeY, pupilSize, pupilSize);
            ctx.fillRect(renderX + this.width - 5 + pupilOffset, eyeY, pupilSize, pupilSize);
            
            // Draw feet (animation)
            if (this.animationFrame === 1) {
                ctx.fillStyle = '#654321';
                ctx.fillRect(renderX + 2, renderY + this.height - 3, 3, 2);
                ctx.fillRect(renderX + this.width - 5, renderY + this.height - 3, 3, 2);
            }
            
            // Draw type-specific features
            if (this.enemyType === 'koopa') {
                // Shell pattern
                ctx.fillStyle = '#32CD32';
                ctx.fillRect(renderX + 2, renderY + 6, this.width - 4, 6);
                
                // Shell segments
                ctx.strokeStyle = '#228B22';
                ctx.lineWidth = 1;
                for (let i = 1; i < 3; i++) {
                    const segmentX = renderX + (i * this.width / 3);
                    ctx.beginPath();
                    ctx.moveTo(segmentX, renderY + 6);
                    ctx.lineTo(segmentX, renderY + 12);
                    ctx.stroke();
                }
            } else if (this.enemyType === 'goomba') {
                // Goomba frown
                ctx.strokeStyle = '#000000';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.moveTo(renderX + 4, renderY + this.height - 6);
                ctx.lineTo(renderX + this.width / 2, renderY + this.height - 4);
                ctx.lineTo(renderX + this.width - 4, renderY + this.height - 6);
                ctx.stroke();
            }
        } else {
            // Squashed state - draw X eyes
            ctx.strokeStyle = '#000000';
            ctx.lineWidth = 2;
            
            // Left X
            ctx.beginPath();
            ctx.moveTo(renderX + 2, renderY + 2);
            ctx.lineTo(renderX + 6, renderY + 6);
            ctx.moveTo(renderX + 6, renderY + 2);
            ctx.lineTo(renderX + 2, renderY + 6);
            ctx.stroke();
            
            // Right X
            ctx.beginPath();
            ctx.moveTo(renderX + this.width - 6, renderY + 2);
            ctx.lineTo(renderX + this.width - 2, renderY + 6);
            ctx.moveTo(renderX + this.width - 2, renderY + 2);
            ctx.lineTo(renderX + this.width - 6, renderY + 6);
            ctx.stroke();
        }
    }
}
