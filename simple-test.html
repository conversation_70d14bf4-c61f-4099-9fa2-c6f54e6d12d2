<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Jump Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        #gameCanvas {
            border: 2px solid #333;
            background: #5C94FC;
            image-rendering: pixelated;
        }
        .info {
            background: white;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>Simple Jump Test</h1>
    <p>Press Space, W, or K to jump. Use A/D or arrow keys to move.</p>
    
    <canvas id="gameCanvas" width="800" height="400"></canvas>
    
    <div class="info" id="info">Loading...</div>
    
    <script type="module">
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const info = document.getElementById('info');
        
        // Simple player object
        const player = {
            x: 100,
            y: 300,
            width: 16,
            height: 16,
            vx: 0,
            vy: 0,
            onGround: false,
            jumpPower: 400,
            gravity: 1200,
            moveSpeed: 200
        };
        
        // Ground
        const ground = {
            x: 0,
            y: 350,
            width: canvas.width,
            height: 50
        };
        
        // Input state
        const keys = {};
        
        // Event listeners
        document.addEventListener('keydown', (e) => {
            keys[e.code] = true;
            console.log('Key down:', e.code);
        });
        
        document.addEventListener('keyup', (e) => {
            keys[e.code] = false;
        });
        
        function isJumpKey(code) {
            return code === 'Space' || code === 'KeyW' || code === 'KeyK';
        }
        
        function isLeftKey(code) {
            return code === 'KeyA' || code === 'ArrowLeft';
        }
        
        function isRightKey(code) {
            return code === 'KeyD' || code === 'ArrowRight';
        }
        
        function update(dt) {
            // Handle input
            let jumpPressed = false;
            let moveInput = 0;
            
            for (let code in keys) {
                if (keys[code]) {
                    if (isJumpKey(code)) jumpPressed = true;
                    if (isLeftKey(code)) moveInput = -1;
                    if (isRightKey(code)) moveInput = 1;
                }
            }
            
            // Movement
            if (moveInput !== 0) {
                player.vx += moveInput * 800 * dt;
                player.vx = Math.max(-player.moveSpeed, Math.min(player.moveSpeed, player.vx));
            } else {
                player.vx *= 0.8; // Friction
            }
            
            // Jumping
            if (jumpPressed && player.onGround) {
                player.vy = -player.jumpPower;
                player.onGround = false;
                console.log('JUMP! vy =', player.vy);
            }
            
            // Gravity
            if (!player.onGround) {
                player.vy += player.gravity * dt;
            }
            
            // Update position
            player.x += player.vx * dt;
            player.y += player.vy * dt;
            
            // Ground collision
            if (player.y + player.height >= ground.y) {
                player.y = ground.y - player.height;
                player.vy = 0;
                player.onGround = true;
            } else {
                player.onGround = false;
            }
            
            // Keep player in bounds
            if (player.x < 0) player.x = 0;
            if (player.x + player.width > canvas.width) player.x = canvas.width - player.width;
            
            // Update info
            info.innerHTML = `
                Position: (${player.x.toFixed(1)}, ${player.y.toFixed(1)})<br>
                Velocity: (${player.vx.toFixed(1)}, ${player.vy.toFixed(1)})<br>
                On Ground: ${player.onGround}<br>
                Jump Pressed: ${jumpPressed}<br>
                Move Input: ${moveInput}
            `;
        }
        
        function render() {
            // Clear canvas
            ctx.fillStyle = '#5C94FC';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Draw ground
            ctx.fillStyle = '#228B22';
            ctx.fillRect(ground.x, ground.y, ground.width, ground.height);
            
            // Draw player
            ctx.fillStyle = player.onGround ? '#0066FF' : '#FF6600';
            ctx.fillRect(player.x, player.y, player.width, player.height);
            
            // Draw player border
            ctx.strokeStyle = '#000033';
            ctx.lineWidth = 1;
            ctx.strokeRect(player.x, player.y, player.width, player.height);
        }
        
        let lastTime = 0;
        function gameLoop(currentTime) {
            const dt = (currentTime - lastTime) / 1000;
            lastTime = currentTime;
            
            if (dt < 0.1) { // Cap delta time
                update(dt);
                render();
            }
            
            requestAnimationFrame(gameLoop);
        }
        
        // Start game loop
        requestAnimationFrame(gameLoop);
        
        console.log('Simple test started');
    </script>
</body>
</html>
