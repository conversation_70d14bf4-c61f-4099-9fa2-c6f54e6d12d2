/**
 * Main entry point for Super Mario Platformer
 * Initializes the game and handles UI interactions
 */

import { Game } from './engine/Game.js';
import { Input } from './engine/Input.js';

class GameManager {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.game = null;
        this.currentLevel = 1;
        
        // UI elements
        this.startScreen = document.getElementById('startScreen');
        this.pauseScreen = document.getElementById('pauseScreen');
        this.gameOverScreen = document.getElementById('gameOverScreen');
        this.winScreen = document.getElementById('winScreen');
        this.hud = document.getElementById('hud');
        
        // Initialize
        this.setupEventListeners();
        this.showStartScreen();
    }
    
    setupEventListeners() {
        // Start screen
        document.getElementById('startButton').addEventListener('click', () => {
            this.startGame();
        });
        
        // Pause screen
        document.getElementById('resumeButton').addEventListener('click', () => {
            this.resumeGame();
        });
        
        document.getElementById('restartButton').addEventListener('click', () => {
            this.restartGame();
        });
        
        // Game over screen
        document.getElementById('retryButton').addEventListener('click', () => {
            this.restartGame();
        });
        
        document.getElementById('menuButton').addEventListener('click', () => {
            this.showStartScreen();
        });
        
        // Win screen
        document.getElementById('nextLevelButton').addEventListener('click', () => {
            this.nextLevel();
        });
        
        document.getElementById('menuButton2').addEventListener('click', () => {
            this.showStartScreen();
        });
        
        // Mute button
        document.getElementById('muteButton').addEventListener('click', () => {
            this.toggleMute();
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.code === 'KeyP' && this.game && this.game.state === 'playing') {
                this.pauseGame();
            } else if (e.code === 'KeyM') {
                this.toggleMute();
            }
        });
    }
    
    startGame() {
        this.hideAllScreens();
        this.showHUD();
        
        this.game = new Game(this.canvas, this.ctx);
        this.game.loadLevel(this.currentLevel);
        this.game.start();
        
        // Setup game event listeners
        this.game.on('gameOver', () => this.showGameOverScreen());
        this.game.on('levelComplete', () => this.showWinScreen());
        this.game.on('scoreUpdate', (score) => this.updateScore(score));
        this.game.on('coinsUpdate', (coins) => this.updateCoins(coins));
        this.game.on('livesUpdate', (lives) => this.updateLives(lives));
    }
    
    pauseGame() {
        if (this.game) {
            this.game.pause();
            this.showPauseScreen();
        }
    }
    
    resumeGame() {
        if (this.game) {
            this.game.resume();
            this.hidePauseScreen();
        }
    }
    
    restartGame() {
        if (this.game) {
            this.game.stop();
        }
        this.startGame();
    }
    
    nextLevel() {
        this.currentLevel++;
        if (this.currentLevel > 2) {
            // Game completed
            this.showStartScreen();
            this.currentLevel = 1;
        } else {
            this.startGame();
        }
    }
    
    toggleMute() {
        if (this.game && this.game.audio) {
            this.game.audio.toggleMute();
            const muteButton = document.getElementById('muteButton');
            muteButton.textContent = this.game.audio.isMuted ? '🔇' : '🔊';
        }
    }
    
    // UI Management
    hideAllScreens() {
        this.startScreen.classList.add('hidden');
        this.pauseScreen.classList.add('hidden');
        this.gameOverScreen.classList.add('hidden');
        this.winScreen.classList.add('hidden');
    }
    
    showStartScreen() {
        this.hideAllScreens();
        this.hideHUD();
        this.startScreen.classList.remove('hidden');
        this.currentLevel = 1;
        if (this.game) {
            this.game.stop();
            this.game = null;
        }
    }
    
    showPauseScreen() {
        this.pauseScreen.classList.remove('hidden');
    }
    
    hidePauseScreen() {
        this.pauseScreen.classList.add('hidden');
    }
    
    showGameOverScreen() {
        this.hideHUD();
        this.gameOverScreen.classList.remove('hidden');
    }
    
    showWinScreen() {
        this.hideHUD();
        const finalScore = document.getElementById('finalScore');
        finalScore.textContent = `Score: ${this.game ? this.game.score : 0}`;
        this.winScreen.classList.remove('hidden');
        
        // Hide next level button if this is the last level
        const nextButton = document.getElementById('nextLevelButton');
        if (this.currentLevel >= 2) {
            nextButton.style.display = 'none';
        } else {
            nextButton.style.display = 'block';
        }
    }
    
    showHUD() {
        this.hud.classList.remove('hidden');
        document.getElementById('level').textContent = this.currentLevel;
    }
    
    hideHUD() {
        this.hud.classList.add('hidden');
    }
    
    // HUD Updates
    updateScore(score) {
        document.getElementById('score').textContent = score;
    }
    
    updateCoins(coins) {
        document.getElementById('coins').textContent = coins;
    }
    
    updateLives(lives) {
        document.getElementById('lives').textContent = lives;
    }
}

// Initialize the game when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new GameManager();
});
