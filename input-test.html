<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Input Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .test-area {
            background: white;
            padding: 20px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .key-display {
            font-family: monospace;
            background: #f0f0f0;
            padding: 10px;
            border-radius: 3px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>Input System Test</h1>
    
    <div class="test-area">
        <h3>Press keys to test:</h3>
        <div class="key-display" id="keyDisplay">No keys pressed</div>
        
        <h3>Action Tests:</h3>
        <div class="key-display" id="actionDisplay">No actions</div>
        
        <h3>Jump Test:</h3>
        <div class="key-display" id="jumpDisplay">Jump not pressed</div>
    </div>
    
    <script type="module">
        console.log('Starting input test...');
        
        let input = null;
        
        try {
            const { Input } = await import('./src/engine/Input.js');
            input = new Input();
            input.init();
            
            console.log('Input system loaded');
            
            const keyDisplay = document.getElementById('keyDisplay');
            const actionDisplay = document.getElementById('actionDisplay');
            const jumpDisplay = document.getElementById('jumpDisplay');
            
            function updateDisplay() {
                if (!input) return;
                
                input.update();
                
                // Show raw key states
                const pressedKeys = [];
                for (let [key, pressed] of input.keys) {
                    if (pressed) pressedKeys.push(key);
                }
                keyDisplay.textContent = pressedKeys.length > 0 ? 
                    `Pressed: ${pressedKeys.join(', ')}` : 'No keys pressed';
                
                // Show action states
                const actions = [];
                if (input.isActionActive('left')) actions.push('LEFT');
                if (input.isActionActive('right')) actions.push('RIGHT');
                if (input.isActionActive('jump')) actions.push('JUMP');
                if (input.isActionActive('pause')) actions.push('PAUSE');
                if (input.isActionActive('mute')) actions.push('MUTE');
                
                actionDisplay.textContent = actions.length > 0 ? 
                    `Actions: ${actions.join(', ')}` : 'No actions';
                
                // Show jump specific
                const jumpPressed = input.isActionPressed('jump');
                const jumpHeld = input.isActionActive('jump');
                jumpDisplay.textContent = `Jump Pressed: ${jumpPressed}, Jump Held: ${jumpHeld}`;
                
                if (jumpPressed) {
                    console.log('JUMP DETECTED!');
                }
            }
            
            // Update display every frame
            setInterval(updateDisplay, 16);
            
            // Add event listeners for debugging
            document.addEventListener('keydown', (e) => {
                console.log('Keydown:', e.code, e.key);
            });
            
            document.addEventListener('keyup', (e) => {
                console.log('Keyup:', e.code, e.key);
            });
            
        } catch (error) {
            console.error('Error loading input system:', error);
        }
    </script>
</body>
</html>
