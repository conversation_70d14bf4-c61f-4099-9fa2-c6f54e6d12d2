# Super Mario Platformer

Một game platformer 2D kiểu Super Mario đơn giản được viết bằng HTML5 Canvas và JavaScript thuần (không framework).

## Tính năng

- **Nhân vật chính**: <PERSON>, nh<PERSON><PERSON> với physics thực tế
- **Hệ thống va chạm**: AABB collision detection với tilemap và entities
- **Thu thập vật phẩm**: Coins và power-ups
- **Kẻ địch**: AI đơn giản, có thể tiêu diệt bằng cách nhảy lên đầu
- **Power-ups**: <PERSON><PERSON><PERSON> kí<PERSON> thước player, chịu được 1 lần va chạm
- **2 màn chơi**: Level 1 (dễ) và Level 2 (kh<PERSON>ơn)
- **Âm thanh**: Procedural sound effects
- **Camera**: Smooth following với shake effects
- **UI**: HUD, menu start/pause/win/lose

## Điều khiển

### Bàn phím
- **A/D** hoặc **←/→**: <PERSON> chuyển trái/phải
- **Space/W/K**: Nhảy
- **P**: Pause/Resume
- **M**: Mute/Unmute

### Mobile (Touch)
- Nút ảo sẽ xuất hiện tự động trên thiết bị di động

## Cách chạy

1. **Sử dụng Live Server (khuyến nghị)**:
   ```bash
   # Nếu có Live Server extension trong VS Code
   # Right-click vào index.html -> "Open with Live Server"
   ```

2. **Sử dụng Python HTTP Server**:
   ```bash
   # Python 3
   python -m http.server 8000
   
   # Sau đó mở http://localhost:8000 trong browser
   ```

3. **Sử dụng Node.js HTTP Server**:
   ```bash
   npx http-server
   ```

4. **Mở trực tiếp** (có thể có lỗi CORS):
   ```
   Mở file index.html trực tiếp trong browser
   ```

## Cấu trúc dự án

```
supermario/
├── index.html              # Entry point
├── styles.css              # Styling
├── assets/                 # Game assets
│   └── README.md           # Asset specifications
├── src/
│   ├── main.js             # Game initialization
│   ├── engine/             # Core game engine
│   │   ├── Game.js         # Main game class
│   │   ├── Input.js        # Input handling
│   │   ├── Camera.js       # Camera system
│   │   ├── Physics.js      # Physics & collision
│   │   └── Audio.js        # Audio system
│   ├── entities/           # Game entities
│   │   ├── Player.js       # Player character
│   │   ├── Enemy.js        # Enemy AI
│   │   ├── Coin.js         # Collectible coins
│   │   ├── PowerUp.js      # Power-up items
│   │   └── Flag.js         # Level completion
│   ├── world/              # Level system
│   │   ├── Tilemap.js      # Tile-based world
│   │   ├── LevelLoader.js  # Level loading
│   │   └── levels/         # Level data
│   │       ├── level1.json
│   │       └── level2.json
│   └── utils/              # Utility classes
│       ├── AABB.js         # Collision detection
│       ├── Math.js         # Math utilities
│       └── Spritesheet.js  # Sprite handling
```

## Game Mechanics

### Player
- **Small Mario**: 16x16 pixels, 1 hit = death
- **Big Mario**: 16x32 pixels, 1 hit = shrink to small
- **Coyote Time**: 80ms grace period for jumping after leaving platform
- **Jump Buffer**: 100ms buffer for jump input before landing

### Enemies
- **Goomba**: Walks left/right, turns at walls/edges
- **Defeat**: Jump on top (player must be falling)
- **Damage**: Touch from side/below

### Items
- **Coins**: +100 points each
- **Mushroom**: Makes player big, +1000 points
- **Flag**: Touch to complete level

### Physics
- **Gravity**: 1200 pixels/second²
- **Terminal Velocity**: 800 pixels/second
- **Jump Power**: 400 pixels/second
- **Move Speed**: 200 pixels/second

## Development

### Adding New Levels
1. Create new JSON file in `src/world/levels/`
2. Define tilemap, entity spawns, and metadata
3. Update LevelLoader.js if needed

### Adding New Entities
1. Create new class in `src/entities/`
2. Implement required methods: `update()`, `render()`, `getBounds()`
3. Add collision handling in `onCollision()`

### Tile Types
- **0**: Air (transparent)
- **1**: Ground (green)
- **2**: Brick (orange)
- **3**: Pipe (dark green)
- **4**: Platform (brown)
- **5**: Background (light green)

### Entity Spawn IDs
- **10**: Player spawn
- **11**: Enemy spawn
- **12**: Coin spawn
- **13**: Power-up spawn
- **14**: Flag spawn

## Browser Compatibility

- **Chrome/Edge**: Full support
- **Firefox**: Full support
- **Safari**: Full support
- **Mobile**: Touch controls supported

## Performance

- **60 FPS** fixed timestep physics
- **Viewport culling** for tiles
- **Entity pooling** for effects
- **Optimized rendering** with canvas

## Known Issues

- Audio may require user interaction to start (browser policy)
- Some mobile browsers may have input lag
- Large levels may impact performance on older devices

## Credits

- **Engine**: Custom HTML5 Canvas engine
- **Graphics**: Procedural colored rectangles (no copyrighted assets)
- **Audio**: Procedural Web Audio API sounds
- **Inspiration**: Classic Super Mario Bros mechanics

## License

This project is for educational purposes. No copyrighted Nintendo assets are used.
