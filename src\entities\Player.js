/**
 * Player entity with movement, jumping, and power-up mechanics
 */

import { AABB } from '../utils/AABB.js';
import { MathUtils } from '../utils/Math.js';

export class Player {
    constructor(x, y) {
        // Position and size
        this.x = x;
        this.y = y;
        this.width = 16;
        this.height = 16;
        this.spawnX = x;
        this.spawnY = y;
        
        // Physics
        this.physics = {
            velocity: { x: 0, y: 0 },
            useGravity: true,
            onGround: false,
            onWall: false,
            onCeiling: false,
            solid: false,
            collidesWithTiles: true,
            collidesWithEntities: true,
            coyoteTime: 0,
            maxCoyoteTime: 0.08, // 80ms
            jumpBuffer: 0,
            maxJumpBuffer: 0.1 // 100ms
        };
        
        // Movement constants
        this.moveSpeed = 200;
        this.jumpPower = 400;
        this.maxSpeed = 250;
        this.acceleration = 800;
        this.friction = 600;
        
        // Player state
        this.state = 'small'; // small, big
        this.facing = 1; // 1 = right, -1 = left
        this.animationState = 'idle'; // idle, run, jump, fall
        this.invulnerable = false;
        this.invulnerabilityTime = 0;
        this.maxInvulnerabilityTime = 2.0;
        
        // Animation
        this.animationTime = 0;
        this.animationFrame = 0;
        
        // Input handling
        this.jumpPressed = false;
        this.jumpHeld = false;
        this.moveInput = 0;
        
        // Game references
        this.game = null;
        
        // Entity properties
        this.destroyed = false;
        this.depth = 10;
    }
    
    /**
     * Update player logic
     */
    update(dt, game) {
        this.game = game;
        
        // Handle input
        this.handleInput(dt, game.input);
        
        // Update timers
        this.updateTimers(dt);
        
        // Update animation
        this.updateAnimation(dt);
        
        // Update physics state
        this.updatePhysicsState(dt);
    }
    
    /**
     * Handle player input
     */
    handleInput(dt, input) {
        // Movement input
        this.moveInput = 0;
        if (input.isActionActive('left')) {
            this.moveInput = -1;
            this.facing = -1;
        } else if (input.isActionActive('right')) {
            this.moveInput = 1;
            this.facing = 1;
        }
        
        // Jump input
        this.jumpPressed = input.isActionPressed('jump');
        this.jumpHeld = input.isActionActive('jump');
        
        // Apply movement
        this.applyMovement(dt);
        
        // Handle jumping
        this.handleJumping(dt);
    }
    
    /**
     * Apply horizontal movement
     */
    applyMovement(dt) {
        if (this.moveInput !== 0) {
            // Accelerate
            this.physics.velocity.x += this.moveInput * this.acceleration * dt;
            
            // Clamp to max speed
            const maxSpeed = this.state === 'big' ? this.maxSpeed * 1.1 : this.maxSpeed;
            this.physics.velocity.x = MathUtils.clamp(this.physics.velocity.x, -maxSpeed, maxSpeed);
            
            // Set animation state
            if (this.physics.onGround) {
                this.animationState = 'run';
            }
        } else {
            // Apply friction
            if (this.physics.onGround) {
                this.physics.velocity.x = MathUtils.applyFriction(this.physics.velocity.x, this.friction, dt);
            }
            
            // Set animation state
            if (this.physics.onGround && Math.abs(this.physics.velocity.x) < 10) {
                this.animationState = 'idle';
            }
        }
    }
    
    /**
     * Handle jumping mechanics
     */
    handleJumping(dt) {
        // Update coyote time
        if (this.physics.onGround) {
            this.physics.coyoteTime = this.physics.maxCoyoteTime;
        } else {
            this.physics.coyoteTime = Math.max(0, this.physics.coyoteTime - dt);
        }
        
        // Update jump buffer
        if (this.jumpPressed) {
            this.physics.jumpBuffer = this.physics.maxJumpBuffer;
        } else {
            this.physics.jumpBuffer = Math.max(0, this.physics.jumpBuffer - dt);
        }
        
        // Perform jump
        if (this.physics.jumpBuffer > 0 && this.physics.coyoteTime > 0) {
            this.jump();
            this.physics.jumpBuffer = 0;
            this.physics.coyoteTime = 0;
        }
        
        // Variable jump height
        if (!this.jumpHeld && this.physics.velocity.y < 0) {
            this.physics.velocity.y *= 0.5;
        }
        
        // Set animation state for air movement
        if (!this.physics.onGround) {
            if (this.physics.velocity.y < 0) {
                this.animationState = 'jump';
            } else {
                this.animationState = 'fall';
            }
        }
    }
    
    /**
     * Perform jump
     */
    jump() {
        this.physics.velocity.y = -this.jumpPower;
        this.physics.onGround = false;

        // Play jump sound
        if (this.game && this.game.audio) {
            this.game.audio.playJumpSound();
        }
    }
    
    /**
     * Update timers
     */
    updateTimers(dt) {
        // Invulnerability timer
        if (this.invulnerable) {
            this.invulnerabilityTime -= dt;
            if (this.invulnerabilityTime <= 0) {
                this.invulnerable = false;
            }
        }
    }
    
    /**
     * Update animation
     */
    updateAnimation(dt) {
        this.animationTime += dt;
        
        // Simple frame animation for running
        if (this.animationState === 'run' && Math.abs(this.physics.velocity.x) > 50) {
            const frameTime = 0.15;
            if (this.animationTime >= frameTime) {
                this.animationFrame = (this.animationFrame + 1) % 2;
                this.animationTime = 0;
            }
        } else {
            this.animationFrame = 0;
            this.animationTime = 0;
        }
    }
    
    /**
     * Update physics state
     */
    updatePhysicsState(dt) {
        // Update bounds for collision detection
        this.updateBounds();
    }
    
    /**
     * Handle collision with other entities
     */
    onCollision(other, axis) {
        if (other.type === 'coin') {
            this.collectCoin(other);
        } else if (other.type === 'powerup') {
            this.collectPowerUp(other);
        } else if (other.type === 'enemy') {
            this.handleEnemyCollision(other, axis);
        }
    }
    
    /**
     * Collect a coin
     */
    collectCoin(coin) {
        if (coin.destroyed) return;
        
        coin.destroyed = true;
        if (this.game) {
            this.game.collectCoin();
        }
    }
    
    /**
     * Collect a power-up
     */
    collectPowerUp(powerUp) {
        if (powerUp.destroyed) return;
        
        powerUp.destroyed = true;
        this.growBig();
        
        if (this.game) {
            this.game.collectPowerUp();
        }
    }
    
    /**
     * Handle collision with enemy
     */
    handleEnemyCollision(enemy, axis) {
        if (enemy.destroyed || this.invulnerable) return;

        // Check if player is stomping enemy (falling down onto enemy)
        if (axis === 'vertical' && this.physics.velocity.y > 0 && this.y < enemy.y) {
            // Stomp enemy
            if (enemy.getSquashed) {
                enemy.getSquashed();
            } else {
                enemy.destroyed = true;
            }
            this.physics.velocity.y = -200; // Bounce

            if (this.game) {
                this.game.addScore(100);
                this.game.audio.playSound('stomp');
            }
        } else {
            // Player hit enemy from side or below
            this.takeDamage();
        }
    }
    
    /**
     * Take damage
     */
    takeDamage() {
        if (this.invulnerable) return;
        
        if (this.state === 'big') {
            // Shrink back to small
            this.shrinkSmall();
            this.makeInvulnerable();
        } else {
            // Player dies
            this.die();
        }
    }
    
    /**
     * Grow to big size
     */
    growBig() {
        if (this.state === 'big') return;
        
        this.state = 'big';
        this.height = 32;
        this.y -= 16; // Adjust position so player doesn't sink into ground
        this.updateBounds();
    }
    
    /**
     * Shrink to small size
     */
    shrinkSmall() {
        if (this.state === 'small') return;
        
        this.state = 'small';
        this.height = 16;
        this.y += 16; // Adjust position
        this.updateBounds();
    }
    
    /**
     * Make player temporarily invulnerable
     */
    makeInvulnerable() {
        this.invulnerable = true;
        this.invulnerabilityTime = this.maxInvulnerabilityTime;
    }
    
    /**
     * Player death
     */
    die() {
        if (this.game) {
            this.game.playerDied();
        }
    }
    
    /**
     * Respawn player
     */
    respawn() {
        this.x = this.spawnX;
        this.y = this.spawnY;
        this.physics.velocity.x = 0;
        this.physics.velocity.y = 0;
        this.physics.onGround = false; // Let physics detect ground
        this.state = 'small';
        this.height = 16;
        this.invulnerable = false;
        this.animationState = 'idle';
        this.updateBounds();
    }
    
    /**
     * Get collision bounds
     */
    getBounds() {
        return new AABB(this.x, this.y, this.width, this.height);
    }
    
    /**
     * Update collision bounds
     */
    updateBounds() {
        // Bounds are calculated dynamically in getBounds()
    }
    
    /**
     * Render player
     */
    render(ctx, alpha) {
        // Calculate render position with interpolation
        const renderX = this.x;
        const renderY = this.y;
        
        // Choose color based on state and invulnerability
        let color = this.state === 'big' ? '#003399' : '#0066FF';
        
        // Flicker when invulnerable
        if (this.invulnerable) {
            const flickerRate = 10;
            if (Math.floor(this.invulnerabilityTime * flickerRate) % 2 === 0) {
                color = 'rgba(0, 102, 255, 0.5)';
            }
        }
        
        // Draw player rectangle
        ctx.fillStyle = color;
        ctx.fillRect(renderX, renderY, this.width, this.height);
        
        // Draw border
        ctx.strokeStyle = '#000033';
        ctx.lineWidth = 1;
        ctx.strokeRect(renderX, renderY, this.width, this.height);
        
        // Draw face direction indicator
        ctx.fillStyle = '#FFFFFF';
        const eyeSize = 2;
        const eyeY = renderY + 4;
        
        if (this.facing === 1) {
            // Facing right
            ctx.fillRect(renderX + this.width - 6, eyeY, eyeSize, eyeSize);
        } else {
            // Facing left
            ctx.fillRect(renderX + 4, eyeY, eyeSize, eyeSize);
        }
        
        // Draw animation frame indicator for running
        if (this.animationState === 'run' && this.animationFrame === 1) {
            ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.fillRect(renderX + 2, renderY + this.height - 4, this.width - 4, 2);
        }
    }
}
