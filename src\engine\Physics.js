/**
 * Physics system for collision detection and response
 */

import { AABB, SweptAABB } from '../utils/AABB.js';
import { MathUtils } from '../utils/Math.js';

export class Physics {
    constructor() {
        // Physics constants
        this.gravity = 1200; // pixels per second squared
        this.terminalVelocity = 800; // maximum falling speed
        this.groundFriction = 0.8; // friction when on ground
        this.airFriction = 0.98; // friction when in air
        
        // Collision layers
        this.LAYER_SOLID = 1;
        this.LAYER_PLATFORM = 2;
        this.LAYER_ENTITY = 4;
        this.LAYER_TRIGGER = 8;
    }
    
    /**
     * Update physics for all entities
     */
    update(dt, entities, tilemap) {
        // Apply physics to each entity
        entities.forEach(entity => {
            if (entity && entity.physics && !entity.destroyed) {
                this.updateEntity(dt, entity, entities, tilemap);
            }
        });
    }
    
    /**
     * Update physics for a single entity
     */
    updateEntity(dt, entity, allEntities, tilemap) {
        const physics = entity.physics;
        
        // Apply gravity
        if (physics.useGravity && !physics.onGround) {
            physics.velocity.y += this.gravity * dt;
            physics.velocity.y = Math.min(physics.velocity.y, this.terminalVelocity);
        }
        
        // Apply friction
        if (physics.onGround) {
            physics.velocity.x *= Math.pow(this.groundFriction, dt);
        } else {
            physics.velocity.x *= Math.pow(this.airFriction, dt);
        }
        
        // Store old position
        const oldX = entity.x;
        const oldY = entity.y;
        
        // Calculate movement
        const deltaX = physics.velocity.x * dt;
        const deltaY = physics.velocity.y * dt;
        
        // Reset collision flags
        physics.onGround = false;
        physics.onWall = false;
        physics.onCeiling = false;
        
        // Move horizontally first
        if (deltaX !== 0) {
            entity.x += deltaX;
            this.handleTilemapCollision(entity, tilemap, 'horizontal');
            this.handleEntityCollisions(entity, allEntities, 'horizontal');
        }
        
        // Then move vertically
        if (deltaY !== 0) {
            entity.y += deltaY;
            this.handleTilemapCollision(entity, tilemap, 'vertical');
            this.handleEntityCollisions(entity, allEntities, 'vertical');
        }
        
        // Update entity bounds
        if (entity.updateBounds) {
            entity.updateBounds();
        }
        
        // Call entity's physics callback if it exists
        if (entity.onPhysicsUpdate) {
            entity.onPhysicsUpdate(dt, oldX, oldY);
        }
    }
    
    /**
     * Handle collision with tilemap
     */
    handleTilemapCollision(entity, tilemap, axis) {
        if (!tilemap || !entity.physics.collidesWithTiles) return;
        
        const bounds = entity.getBounds();
        const tileSize = tilemap.tileSize;
        
        // Get tiles in collision area
        const startX = Math.floor(bounds.left / tileSize);
        const endX = Math.floor(bounds.right / tileSize);
        const startY = Math.floor(bounds.top / tileSize);
        const endY = Math.floor(bounds.bottom / tileSize);
        
        for (let ty = startY; ty <= endY; ty++) {
            for (let tx = startX; tx <= endX; tx++) {
                const tile = tilemap.getTile(tx, ty);
                
                if (tile && tile.solid) {
                    const tileBounds = new AABB(
                        tx * tileSize,
                        ty * tileSize,
                        tileSize,
                        tileSize
                    );
                    
                    if (bounds.intersects(tileBounds)) {
                        this.resolveTileCollision(entity, tileBounds, axis);
                    }
                }
            }
        }
    }
    
    /**
     * Resolve collision with a tile
     */
    resolveTileCollision(entity, tileBounds, axis) {
        const bounds = entity.getBounds();
        const physics = entity.physics;
        const overlap = bounds.getOverlap(tileBounds);
        
        if (axis === 'horizontal') {
            if (overlap.width > 0) {
                if (physics.velocity.x > 0) {
                    // Moving right, hit left side of tile
                    entity.x = tileBounds.left - entity.width;
                    physics.onWall = true;
                } else if (physics.velocity.x < 0) {
                    // Moving left, hit right side of tile
                    entity.x = tileBounds.right;
                    physics.onWall = true;
                }
                physics.velocity.x = 0;
            }
        } else if (axis === 'vertical') {
            if (overlap.height > 0) {
                if (physics.velocity.y > 0) {
                    // Moving down, hit top of tile
                    entity.y = tileBounds.top - entity.height;
                    physics.onGround = true;
                    physics.coyoteTime = physics.maxCoyoteTime || 0;
                } else if (physics.velocity.y < 0) {
                    // Moving up, hit bottom of tile
                    entity.y = tileBounds.bottom;
                    physics.onCeiling = true;
                }
                physics.velocity.y = 0;
            }
        }
    }
    
    /**
     * Handle collisions with other entities
     */
    handleEntityCollisions(entity, allEntities, axis) {
        if (!entity.physics.collidesWithEntities) return;
        
        const bounds = entity.getBounds();
        
        allEntities.forEach(other => {
            if (other === entity || other.destroyed || !other.physics) return;
            
            const otherBounds = other.getBounds();
            if (bounds.intersects(otherBounds)) {
                this.handleEntityCollision(entity, other, axis);
            }
        });
    }
    
    /**
     * Handle collision between two entities
     */
    handleEntityCollision(entity1, entity2, axis) {
        // Call collision callbacks
        if (entity1.onCollision) {
            entity1.onCollision(entity2, axis);
        }
        
        if (entity2.onCollision) {
            entity2.onCollision(entity1, axis);
        }
        
        // Handle solid entity collision
        if (entity1.physics.solid && entity2.physics.solid) {
            this.resolveSolidCollision(entity1, entity2, axis);
        }
    }
    
    /**
     * Resolve collision between two solid entities
     */
    resolveSolidCollision(entity1, entity2, axis) {
        const bounds1 = entity1.getBounds();
        const bounds2 = entity2.getBounds();
        const overlap = bounds1.getOverlap(bounds2);
        
        if (axis === 'horizontal' && overlap.width > 0) {
            const separation = overlap.width / 2;
            
            if (entity1.physics.velocity.x > 0) {
                entity1.x -= separation;
                entity2.x += separation;
            } else {
                entity1.x += separation;
                entity2.x -= separation;
            }
            
            // Exchange velocities (simple elastic collision)
            const temp = entity1.physics.velocity.x;
            entity1.physics.velocity.x = entity2.physics.velocity.x;
            entity2.physics.velocity.x = temp;
            
        } else if (axis === 'vertical' && overlap.height > 0) {
            const separation = overlap.height / 2;
            
            if (entity1.physics.velocity.y > 0) {
                entity1.y -= separation;
                entity2.y += separation;
                entity1.physics.onGround = true;
            } else {
                entity1.y += separation;
                entity2.y -= separation;
                entity2.physics.onGround = true;
            }
            
            // Exchange velocities
            const temp = entity1.physics.velocity.y;
            entity1.physics.velocity.y = entity2.physics.velocity.y;
            entity2.physics.velocity.y = temp;
        }
    }
    
    /**
     * Check if an entity is on ground
     */
    isOnGround(entity, tilemap) {
        if (!tilemap) return false;
        
        const bounds = entity.getBounds();
        const tileSize = tilemap.tileSize;
        
        // Check one pixel below the entity
        const checkY = bounds.bottom + 1;
        const startX = Math.floor(bounds.left / tileSize);
        const endX = Math.floor(bounds.right / tileSize);
        const tileY = Math.floor(checkY / tileSize);
        
        for (let tx = startX; tx <= endX; tx++) {
            const tile = tilemap.getTile(tx, tileY);
            if (tile && tile.solid) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Raycast from point in direction
     */
    raycast(startX, startY, dirX, dirY, maxDistance, tilemap) {
        const step = 4; // Step size for raycast
        const steps = Math.floor(maxDistance / step);
        
        for (let i = 0; i < steps; i++) {
            const x = startX + dirX * i * step;
            const y = startY + dirY * i * step;
            
            if (tilemap) {
                const tileX = Math.floor(x / tilemap.tileSize);
                const tileY = Math.floor(y / tilemap.tileSize);
                const tile = tilemap.getTile(tileX, tileY);
                
                if (tile && tile.solid) {
                    return {
                        hit: true,
                        x: x,
                        y: y,
                        distance: i * step,
                        tileX: tileX,
                        tileY: tileY
                    };
                }
            }
        }
        
        return {
            hit: false,
            x: startX + dirX * maxDistance,
            y: startY + dirY * maxDistance,
            distance: maxDistance
        };
    }
    
    /**
     * Apply impulse to entity
     */
    applyImpulse(entity, impulseX, impulseY) {
        if (entity.physics) {
            entity.physics.velocity.x += impulseX;
            entity.physics.velocity.y += impulseY;
        }
    }
    
    /**
     * Set entity velocity
     */
    setVelocity(entity, velocityX, velocityY) {
        if (entity.physics) {
            entity.physics.velocity.x = velocityX;
            entity.physics.velocity.y = velocityY;
        }
    }
}
