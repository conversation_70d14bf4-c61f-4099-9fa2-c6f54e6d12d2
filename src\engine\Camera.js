/**
 * Camera system for following the player and managing viewport
 */

import { MathUtils } from '../utils/Math.js';

export class Camera {
    constructor(viewportWidth, viewportHeight) {
        // Viewport dimensions
        this.viewportWidth = viewportWidth;
        this.viewportHeight = viewportHeight;
        
        // Camera position (top-left corner of viewport)
        this.x = 0;
        this.y = 0;
        
        // Target to follow
        this.target = null;
        
        // Camera bounds (world limits)
        this.minX = 0;
        this.minY = 0;
        this.maxX = 0;
        this.maxY = 0;
        
        // Camera settings
        this.followSpeed = 5.0; // How fast camera follows target
        this.deadZone = {
            x: 100, // Horizontal dead zone
            y: 50   // Vertical dead zone
        };
        
        // Camera offset from target
        this.offset = {
            x: 0,
            y: -50 // Look slightly ahead vertically
        };
        
        // Smooth movement
        this.smoothing = true;
        this.targetX = 0;
        this.targetY = 0;
        
        // Shake effect
        this.shake = {
            intensity: 0,
            duration: 0,
            currentTime: 0,
            offsetX: 0,
            offsetY: 0
        };
        
        // Zoom (for future use)
        this.zoom = 1.0;
        this.targetZoom = 1.0;
    }
    
    /**
     * Set the target entity for the camera to follow
     */
    setTarget(target) {
        this.target = target;
        if (target) {
            // Immediately snap to target position
            this.snapToTarget();
        }
    }
    
    /**
     * Set camera bounds (world limits)
     */
    setBounds(minX, minY, maxX, maxY) {
        this.minX = minX;
        this.minY = minY;
        this.maxX = Math.max(maxX - this.viewportWidth, minX);
        this.maxY = Math.max(maxY - this.viewportHeight, minY);
    }
    
    /**
     * Immediately snap camera to target position
     */
    snapToTarget() {
        if (!this.target) return;
        
        const targetX = this.target.x + this.target.width / 2 - this.viewportWidth / 2 + this.offset.x;
        const targetY = this.target.y + this.target.height / 2 - this.viewportHeight / 2 + this.offset.y;
        
        this.x = MathUtils.clamp(targetX, this.minX, this.maxX);
        this.y = MathUtils.clamp(targetY, this.minY, this.maxY);
        
        this.targetX = this.x;
        this.targetY = this.y;
    }
    
    /**
     * Update camera position
     */
    update(dt) {
        if (this.target) {
            this.updateFollowTarget(dt);
        }
        
        this.updateShake(dt);
        this.updateZoom(dt);
    }
    
    /**
     * Update camera to follow target with dead zone
     */
    updateFollowTarget(dt) {
        const targetCenterX = this.target.x + this.target.width / 2;
        const targetCenterY = this.target.y + this.target.height / 2;
        
        // Calculate camera center
        const cameraCenterX = this.x + this.viewportWidth / 2;
        const cameraCenterY = this.y + this.viewportHeight / 2;
        
        // Calculate distance from target to camera center
        const deltaX = targetCenterX - cameraCenterX + this.offset.x;
        const deltaY = targetCenterY - cameraCenterY + this.offset.y;
        
        // Apply dead zone
        let moveX = 0;
        let moveY = 0;
        
        if (Math.abs(deltaX) > this.deadZone.x) {
            moveX = deltaX - Math.sign(deltaX) * this.deadZone.x;
        }
        
        if (Math.abs(deltaY) > this.deadZone.y) {
            moveY = deltaY - Math.sign(deltaY) * this.deadZone.y;
        }
        
        // Calculate target position
        this.targetX = this.x + moveX;
        this.targetY = this.y + moveY;
        
        // Apply bounds
        this.targetX = MathUtils.clamp(this.targetX, this.minX, this.maxX);
        this.targetY = MathUtils.clamp(this.targetY, this.minY, this.maxY);
        
        // Smooth movement
        if (this.smoothing) {
            const speed = this.followSpeed * dt;
            this.x = MathUtils.lerp(this.x, this.targetX, speed);
            this.y = MathUtils.lerp(this.y, this.targetY, speed);
        } else {
            this.x = this.targetX;
            this.y = this.targetY;
        }
    }
    
    /**
     * Update camera shake effect
     */
    updateShake(dt) {
        if (this.shake.duration <= 0) {
            this.shake.offsetX = 0;
            this.shake.offsetY = 0;
            return;
        }
        
        this.shake.currentTime += dt;
        
        if (this.shake.currentTime >= this.shake.duration) {
            this.shake.duration = 0;
            this.shake.offsetX = 0;
            this.shake.offsetY = 0;
        } else {
            // Generate random shake offset
            const intensity = this.shake.intensity * (1 - this.shake.currentTime / this.shake.duration);
            this.shake.offsetX = (Math.random() - 0.5) * intensity * 2;
            this.shake.offsetY = (Math.random() - 0.5) * intensity * 2;
        }
    }
    
    /**
     * Update camera zoom
     */
    updateZoom(dt) {
        if (this.zoom !== this.targetZoom) {
            const zoomSpeed = 2.0 * dt;
            this.zoom = MathUtils.lerp(this.zoom, this.targetZoom, zoomSpeed);
            
            if (Math.abs(this.zoom - this.targetZoom) < 0.01) {
                this.zoom = this.targetZoom;
            }
        }
    }
    
    /**
     * Start camera shake effect
     */
    startShake(intensity, duration) {
        this.shake.intensity = intensity;
        this.shake.duration = duration;
        this.shake.currentTime = 0;
    }
    
    /**
     * Set camera zoom
     */
    setZoom(zoom) {
        this.targetZoom = MathUtils.clamp(zoom, 0.5, 3.0);
    }
    
    /**
     * Apply camera transform to rendering context
     */
    apply(ctx) {
        const finalX = Math.round(this.x + this.shake.offsetX);
        const finalY = Math.round(this.y + this.shake.offsetY);
        
        ctx.scale(this.zoom, this.zoom);
        ctx.translate(-finalX / this.zoom, -finalY / this.zoom);
    }
    
    /**
     * Convert world coordinates to screen coordinates
     */
    worldToScreen(worldX, worldY) {
        return {
            x: (worldX - this.x) * this.zoom,
            y: (worldY - this.y) * this.zoom
        };
    }
    
    /**
     * Convert screen coordinates to world coordinates
     */
    screenToWorld(screenX, screenY) {
        return {
            x: screenX / this.zoom + this.x,
            y: screenY / this.zoom + this.y
        };
    }
    
    /**
     * Check if a rectangle is visible in the camera viewport
     */
    isVisible(x, y, width, height) {
        const margin = 50; // Add some margin for smooth culling
        
        return !(x + width < this.x - margin ||
                x > this.x + this.viewportWidth + margin ||
                y + height < this.y - margin ||
                y > this.y + this.viewportHeight + margin);
    }
    
    /**
     * Get the camera's viewport bounds in world coordinates
     */
    getViewportBounds() {
        return {
            x: this.x,
            y: this.y,
            width: this.viewportWidth / this.zoom,
            height: this.viewportHeight / this.zoom
        };
    }
    
    /**
     * Move camera to a specific position
     */
    moveTo(x, y, smooth = true) {
        if (smooth) {
            this.targetX = MathUtils.clamp(x, this.minX, this.maxX);
            this.targetY = MathUtils.clamp(y, this.minY, this.maxY);
        } else {
            this.x = MathUtils.clamp(x, this.minX, this.maxX);
            this.y = MathUtils.clamp(y, this.minY, this.maxY);
            this.targetX = this.x;
            this.targetY = this.y;
        }
    }
    
    /**
     * Get camera center position
     */
    getCenter() {
        return {
            x: this.x + this.viewportWidth / 2,
            y: this.y + this.viewportHeight / 2
        };
    }
}
