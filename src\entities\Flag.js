/**
 * Flag entity - level completion trigger
 */

import { AABB } from '../utils/AABB.js';

export class Flag {
    constructor(x, y) {
        // Position and size
        this.x = x;
        this.y = y;
        this.width = 16;
        this.height = 48;
        this.poleWidth = 4;
        
        // Physics (flags don't use physics)
        this.physics = {
            velocity: { x: 0, y: 0 },
            useGravity: false,
            onGround: false,
            onWall: false,
            onCeiling: false,
            solid: false,
            collidesWithTiles: false,
            collidesWithEntities: true
        };
        
        // Flag properties
        this.type = 'flag';
        this.touched = false;
        this.flagHeight = 16;
        this.flagWidth = 12;
        
        // Animation
        this.animationTime = 0;
        this.waveOffset = 0;
        this.waveAmplitude = 2;
        this.waveFrequency = 4;
        
        // Flag lowering animation
        this.flagY = this.y + 4; // Start position of flag
        this.targetFlagY = this.y + this.height - this.flagHeight - 4;
        this.loweringSpeed = 60; // pixels per second
        
        // Victory effect
        this.victoryTime = 0;
        this.showVictoryEffect = false;
        
        // Entity properties
        this.destroyed = false;
        this.depth = 0;
        
        // Visual effects
        this.sparkles = [];
        this.sparkleTime = 0;
    }
    
    /**
     * Update flag logic
     */
    update(dt, game) {
        // Update animation
        this.updateAnimation(dt);
        
        // Update flag lowering
        if (this.touched) {
            this.updateFlagLowering(dt);
            this.updateVictoryEffect(dt);
        }
        
        // Update visual effects
        this.updateSparkles(dt);
        
        // Update physics state
        this.updatePhysicsState(dt);
    }
    
    /**
     * Update flag animation
     */
    updateAnimation(dt) {
        this.animationTime += dt;
        
        // Wave animation for flag
        this.waveOffset = Math.sin(this.animationTime * this.waveFrequency) * this.waveAmplitude;
        
        // Generate sparkles when touched
        if (this.touched) {
            this.sparkleTime += dt;
            if (this.sparkleTime >= 0.1) {
                this.addSparkle();
                this.sparkleTime = 0;
            }
        }
    }
    
    /**
     * Update flag lowering animation
     */
    updateFlagLowering(dt) {
        if (this.flagY < this.targetFlagY) {
            this.flagY += this.loweringSpeed * dt;
            if (this.flagY >= this.targetFlagY) {
                this.flagY = this.targetFlagY;
            }
        }
    }
    
    /**
     * Update victory effect
     */
    updateVictoryEffect(dt) {
        if (this.touched) {
            this.victoryTime += dt;
            this.showVictoryEffect = true;
        }
    }
    
    /**
     * Update sparkle effects
     */
    updateSparkles(dt) {
        this.sparkles = this.sparkles.filter(sparkle => {
            sparkle.life -= dt;
            sparkle.x += sparkle.vx * dt;
            sparkle.y += sparkle.vy * dt;
            sparkle.vy += 50 * dt; // Light gravity
            return sparkle.life > 0;
        });
    }
    
    /**
     * Add a sparkle effect
     */
    addSparkle() {
        const sparkle = {
            x: this.x + this.poleWidth + Math.random() * this.flagWidth,
            y: this.flagY + Math.random() * this.flagHeight,
            vx: (Math.random() - 0.5) * 30,
            vy: -Math.random() * 30 - 10,
            life: 0.5 + Math.random() * 0.5,
            maxLife: 1.0,
            size: 1 + Math.random() * 2,
            color: Math.random() > 0.5 ? '#FFD700' : '#FF0000'
        };
        
        this.sparkles.push(sparkle);
    }
    
    /**
     * Update physics state
     */
    updatePhysicsState(dt) {
        // Flags don't move, so no physics updates needed
    }
    
    /**
     * Handle collision with other entities
     */
    onCollision(other, axis) {
        if (other.type === 'player' && !this.touched) {
            this.touch();
        }
    }
    
    /**
     * Touch the flag (level completion)
     */
    touch() {
        if (this.touched) return;
        
        this.touched = true;
        this.victoryTime = 0;
        
        // Add initial burst of sparkles
        for (let i = 0; i < 15; i++) {
            this.addSparkle();
        }
    }
    
    /**
     * Get collision bounds
     */
    getBounds() {
        return new AABB(this.x, this.y, this.width, this.height);
    }
    
    /**
     * Update collision bounds
     */
    updateBounds() {
        // Bounds are calculated dynamically in getBounds()
    }
    
    /**
     * Render flag
     */
    render(ctx, alpha) {
        if (this.destroyed) return;
        
        const renderX = this.x;
        const renderY = this.y;
        
        // Render flag pole
        this.renderPole(ctx, renderX, renderY);
        
        // Render flag
        this.renderFlag(ctx, renderX, renderY);
        
        // Render sparkles
        this.renderSparkles(ctx);
        
        // Render victory effect
        if (this.showVictoryEffect) {
            this.renderVictoryEffect(ctx, renderX, renderY);
        }
    }
    
    /**
     * Render flag pole
     */
    renderPole(ctx, x, y) {
        // Pole
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(x, y, this.poleWidth, this.height);
        
        // Pole highlights
        ctx.fillStyle = '#CD853F';
        ctx.fillRect(x, y, 1, this.height);
        
        // Pole border
        ctx.strokeStyle = '#654321';
        ctx.lineWidth = 1;
        ctx.strokeRect(x, y, this.poleWidth, this.height);
        
        // Pole top ornament
        ctx.fillStyle = '#FFD700';
        ctx.beginPath();
        ctx.arc(x + this.poleWidth / 2, y, 3, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.strokeStyle = '#FFA500';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.arc(x + this.poleWidth / 2, y, 3, 0, Math.PI * 2);
        ctx.stroke();
    }
    
    /**
     * Render flag
     */
    renderFlag(ctx, x, y) {
        const flagX = x + this.poleWidth;
        const flagY = this.flagY;
        
        ctx.save();
        
        // Flag body
        ctx.fillStyle = '#FF0000';
        ctx.beginPath();
        ctx.moveTo(flagX, flagY);
        
        // Create wavy flag effect
        const segments = 8;
        for (let i = 0; i <= segments; i++) {
            const segmentY = flagY + (i / segments) * this.flagHeight;
            const waveX = flagX + this.flagWidth + Math.sin(this.animationTime * 6 + i * 0.5) * this.waveOffset;
            
            if (i === 0) {
                ctx.lineTo(waveX, segmentY);
            } else {
                ctx.lineTo(waveX, segmentY);
            }
        }
        
        ctx.lineTo(flagX, flagY + this.flagHeight);
        ctx.closePath();
        ctx.fill();
        
        // Flag border
        ctx.strokeStyle = '#CC0000';
        ctx.lineWidth = 1;
        ctx.stroke();
        
        // Flag pattern (stripes or symbol)
        ctx.fillStyle = '#FFFFFF';
        for (let i = 1; i < this.flagHeight; i += 4) {
            ctx.fillRect(flagX + 1, flagY + i, this.flagWidth - 2, 2);
        }
        
        // Flag attachment to pole
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(flagX - 2, flagY, 2, 4);
        ctx.fillRect(flagX - 2, flagY + this.flagHeight - 4, 2, 4);
        
        ctx.restore();
    }
    
    /**
     * Render sparkle effects
     */
    renderSparkles(ctx) {
        this.sparkles.forEach(sparkle => {
            const alpha = sparkle.life / sparkle.maxLife;
            
            ctx.save();
            ctx.globalAlpha = alpha;
            ctx.fillStyle = sparkle.color;
            
            // Draw sparkle as a small star
            ctx.beginPath();
            ctx.arc(sparkle.x, sparkle.y, sparkle.size, 0, Math.PI * 2);
            ctx.fill();
            
            // Add cross pattern
            ctx.strokeStyle = '#FFFFFF';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(sparkle.x - sparkle.size, sparkle.y);
            ctx.lineTo(sparkle.x + sparkle.size, sparkle.y);
            ctx.moveTo(sparkle.x, sparkle.y - sparkle.size);
            ctx.lineTo(sparkle.x, sparkle.y + sparkle.size);
            ctx.stroke();
            
            ctx.restore();
        });
    }
    
    /**
     * Render victory effect
     */
    renderVictoryEffect(ctx, x, y) {
        if (!this.touched) return;
        
        const centerX = x + this.width / 2;
        const centerY = y + this.height / 2;
        
        // Pulsing glow effect
        const glowRadius = 30 + Math.sin(this.victoryTime * 8) * 10;
        const glowAlpha = 0.3 + Math.sin(this.victoryTime * 6) * 0.2;
        
        ctx.save();
        ctx.globalAlpha = glowAlpha;
        
        // Create radial gradient
        const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, glowRadius);
        gradient.addColorStop(0, '#FFD700');
        gradient.addColorStop(0.5, '#FFA500');
        gradient.addColorStop(1, 'rgba(255, 165, 0, 0)');
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(centerX, centerY, glowRadius, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.restore();
        
        // Victory text (appears after a delay)
        if (this.victoryTime > 1.0) {
            ctx.save();
            ctx.fillStyle = '#FFD700';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.strokeStyle = '#000000';
            ctx.lineWidth = 2;
            
            const text = 'LEVEL COMPLETE!';
            const textY = y - 30;
            
            ctx.strokeText(text, centerX, textY);
            ctx.fillText(text, centerX, textY);
            
            ctx.restore();
        }
    }
}
