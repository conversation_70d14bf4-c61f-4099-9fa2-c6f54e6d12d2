/* Reset và base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Courier New', monospace;
    background: linear-gradient(135deg, #87CEEB, #98FB98);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    overflow: hidden;
}

#gameContainer {
    position: relative;
    border: 4px solid #333;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    background: #000;
}

#gameCanvas {
    display: block;
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
    background: #5C94FC;
}

/* Screen overlays */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    z-index: 10;
}

.screen.hidden {
    display: none;
}

.screen h1 {
    font-size: 3em;
    margin-bottom: 30px;
    text-shadow: 3px 3px 0px #333;
    color: #FFD700;
}

.screen h2 {
    font-size: 2.5em;
    margin-bottom: 20px;
    text-shadow: 2px 2px 0px #333;
    color: #FFD700;
}

.screen button {
    font-family: 'Courier New', monospace;
    font-size: 1.2em;
    padding: 12px 24px;
    margin: 8px;
    border: 3px solid #333;
    border-radius: 8px;
    background: #FFD700;
    color: #333;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: bold;
}

.screen button:hover {
    background: #FFA500;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.screen button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.controls {
    margin-top: 30px;
    text-align: center;
    font-size: 1.1em;
    line-height: 1.6;
}

.controls p {
    margin: 5px 0;
}

/* HUD */
#hud {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
    font-size: 1.2em;
    font-weight: bold;
    text-shadow: 2px 2px 0px #333;
    z-index: 5;
    pointer-events: none;
}

#hud.hidden {
    display: none;
}

.hud-item {
    background: rgba(0, 0, 0, 0.6);
    padding: 8px 12px;
    border-radius: 6px;
    border: 2px solid #333;
}

#muteButton {
    font-size: 1.2em;
    background: rgba(0, 0, 0, 0.6);
    border: 2px solid #333;
    border-radius: 6px;
    color: white;
    cursor: pointer;
    padding: 8px 12px;
    pointer-events: auto;
    transition: background 0.2s;
}

#muteButton:hover {
    background: rgba(0, 0, 0, 0.8);
}

/* Responsive design */
@media (max-width: 1200px) {
    #gameCanvas {
        width: 90vw;
        height: auto;
    }
    
    .screen h1 {
        font-size: 2.5em;
    }
    
    .screen h2 {
        font-size: 2em;
    }
    
    .screen button {
        font-size: 1em;
        padding: 10px 20px;
    }
    
    #hud {
        font-size: 1em;
    }
}

@media (max-width: 768px) {
    #gameCanvas {
        width: 95vw;
    }
    
    .screen h1 {
        font-size: 2em;
    }
    
    .screen h2 {
        font-size: 1.5em;
    }
    
    .screen button {
        font-size: 0.9em;
        padding: 8px 16px;
    }
    
    #hud {
        font-size: 0.9em;
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .controls {
        font-size: 1em;
    }
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

.fade-out {
    animation: fadeOut 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

.bounce {
    animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 60%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    80% { transform: translateY(-5px); }
}
