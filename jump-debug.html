<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jump Debug</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        #gameCanvas {
            border: 2px solid #333;
            background: #5C94FC;
            image-rendering: pixelated;
        }
        .debug {
            background: white;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Jump Debug Test</h1>
    <p><strong>Press Space, W, or K to jump!</strong></p>
    
    <canvas id="gameCanvas" width="800" height="400"></canvas>
    
    <div class="debug" id="debugInfo">Loading...</div>
    
    <script type="module">
        console.log('Starting jump debug...');
        
        let game = null;
        let debugInfo = document.getElementById('debugInfo');
        
        function updateDebugInfo() {
            if (!game || !game.player) {
                debugInfo.innerHTML = 'Game not loaded yet...';
                return;
            }
            
            const player = game.player;
            const input = game.input;
            
            // Check all possible jump keys
            const spacePressed = input.keys.get('Space');
            const wPressed = input.keys.get('KeyW');
            const kPressed = input.keys.get('KeyK');
            
            const jumpActionPressed = input.isActionPressed('jump');
            const jumpActionActive = input.isActionActive('jump');
            
            const info = `
                <strong>Player State:</strong><br>
                Position: (${player.x.toFixed(1)}, ${player.y.toFixed(1)})<br>
                Velocity: (${player.physics.velocity.x.toFixed(1)}, ${player.physics.velocity.y.toFixed(1)})<br>
                On Ground: ${player.physics.onGround}<br>
                Coyote Time: ${player.physics.coyoteTime.toFixed(3)}<br>
                Jump Buffer: ${player.physics.jumpBuffer.toFixed(3)}<br>
                <br>
                <strong>Raw Key States:</strong><br>
                Space: ${spacePressed}<br>
                KeyW: ${wPressed}<br>
                KeyK: ${kPressed}<br>
                <br>
                <strong>Jump Actions:</strong><br>
                Jump Pressed: ${jumpActionPressed}<br>
                Jump Active: ${jumpActionActive}<br>
                Player jumpPressed: ${player.jumpPressed}<br>
                Player jumpHeld: ${player.jumpHeld}<br>
                <br>
                <strong>Jump Conditions:</strong><br>
                Can Jump: ${player.physics.jumpBuffer > 0 && player.physics.coyoteTime > 0}<br>
                Jump Buffer > 0: ${player.physics.jumpBuffer > 0}<br>
                Coyote Time > 0: ${player.physics.coyoteTime > 0}<br>
            `;
            
            debugInfo.innerHTML = info;
        }
        
        try {
            const { Game } = await import('./src/engine/Game.js');
            
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            game = new Game(canvas, ctx);
            
            console.log('Game instance created');
            
            // Load level 1
            await game.loadLevel(1);
            console.log('Level loaded');
            
            // Start game
            game.start();
            console.log('Game started');
            
            // Debug info update loop
            setInterval(updateDebugInfo, 50);
            
            // Add keyboard event listener for debugging
            document.addEventListener('keydown', (e) => {
                console.log('Key pressed:', e.code);
                if (e.code === 'Space' || e.code === 'KeyW' || e.code === 'KeyK') {
                    console.log('JUMP KEY DETECTED!', e.code);
                    console.log('Player onGround:', game.player?.physics.onGround);
                    console.log('Player coyoteTime:', game.player?.physics.coyoteTime);
                }
            });
            
            document.addEventListener('keyup', (e) => {
                console.log('Key released:', e.code);
            });
            
        } catch (error) {
            console.error('Error:', error);
            debugInfo.innerHTML = `Error: ${error.message}`;
        }
    </script>
</body>
</html>
