<!DOCTYPE html>
<html>
<head>
    <title>Jump Height Test</title>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial; }
        canvas { border: 2px solid #000; background: #87CEEB; }
        .info { background: #f0f0f0; padding: 10px; margin: 10px 0; font-family: monospace; }
    </style>
</head>
<body>
    <h1>Jump Height Test</h1>
    <p>Press SPACE to jump. Check if you can reach the coins!</p>
    <canvas id="canvas" width="800" height="600"></canvas>
    <div class="info" id="info">Jump info...</div>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        const info = document.getElementById('info');

        // Player with same physics as game
        const player = {
            x: 100,
            y: 528, // Same as game spawn
            width: 16,
            height: 16,
            vx: 0,
            vy: 0,
            onGround: false,
            jumpPower: 700, // Same as game
            gravity: 1000, // Same as game
            speed: 200,
            maxJumpHeight: 0,
            startY: 528
        };

        // Ground at same level as game
        const groundY = 544;

        // Coins at different heights (same as level1.json)
        const coins = [
            { x: 200, y: 352, collected: false, label: "y=352" },
            { x: 300, y: 320, collected: false, label: "y=320" },
            { x: 400, y: 288, collected: false, label: "y=288" },
            { x: 500, y: 480, collected: false, label: "y=480" }
        ];

        // Platforms
        const platforms = [
            { x: 180, y: 384, width: 160, height: 32 }, // Platform for coins
        ];

        // Input
        const keys = {};
        let jumpPressed = false;

        document.addEventListener('keydown', (e) => {
            keys[e.code] = true;
            if (e.code === 'Space') {
                jumpPressed = true;
            }
        });

        document.addEventListener('keyup', (e) => {
            keys[e.code] = false;
        });

        function update(dt) {
            const wasJumpPressed = jumpPressed;
            jumpPressed = false;

            // Movement
            if (keys['KeyA']) player.vx = -player.speed;
            else if (keys['KeyD']) player.vx = player.speed;
            else player.vx *= 0.8;

            // Jump
            if (wasJumpPressed && player.onGround) {
                player.vy = -player.jumpPower;
                player.onGround = false;
                player.startY = player.y;
                player.maxJumpHeight = 0;
            }

            // Gravity
            if (!player.onGround) {
                player.vy += player.gravity * dt;
                
                // Track max jump height
                const currentHeight = player.startY - player.y;
                if (currentHeight > player.maxJumpHeight) {
                    player.maxJumpHeight = currentHeight;
                }
            }

            // Update position
            player.x += player.vx * dt;
            player.y += player.vy * dt;

            // Ground collision
            if (player.y + player.height >= groundY) {
                player.y = groundY - player.height;
                player.vy = 0;
                player.onGround = true;
            } else {
                player.onGround = false;
            }

            // Platform collision
            platforms.forEach(platform => {
                if (player.x + player.width > platform.x && 
                    player.x < platform.x + platform.width &&
                    player.y + player.height > platform.y && 
                    player.y < platform.y + platform.height &&
                    player.vy > 0) {
                    
                    player.y = platform.y - player.height;
                    player.vy = 0;
                    player.onGround = true;
                }
            });

            // Coin collection
            coins.forEach(coin => {
                if (!coin.collected &&
                    player.x + player.width > coin.x && 
                    player.x < coin.x + 16 &&
                    player.y + player.height > coin.y && 
                    player.y < coin.y + 16) {
                    coin.collected = true;
                }
            });

            // Bounds
            if (player.x < 0) player.x = 0;
            if (player.x > canvas.width - player.width) player.x = canvas.width - player.width;

            // Info
            info.innerHTML = `
                Position: (${player.x.toFixed(0)}, ${player.y.toFixed(0)})<br>
                Velocity: (${player.vx.toFixed(0)}, ${player.vy.toFixed(0)})<br>
                On Ground: ${player.onGround}<br>
                Max Jump Height: ${player.maxJumpHeight.toFixed(0)} pixels<br>
                Can reach y=${player.startY - player.maxJumpHeight.toFixed(0)}<br>
                Jump Power: ${player.jumpPower}, Gravity: ${player.gravity}<br>
                Coins collected: ${coins.filter(c => c.collected).length}/${coins.length}
            `;
        }

        function render() {
            // Clear
            ctx.fillStyle = '#87CEEB';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Ground
            ctx.fillStyle = '#228B22';
            ctx.fillRect(0, groundY, canvas.width, canvas.height - groundY);

            // Platforms
            ctx.fillStyle = '#FF8C00';
            platforms.forEach(platform => {
                ctx.fillRect(platform.x, platform.y, platform.width, platform.height);
            });

            // Coins
            coins.forEach(coin => {
                if (!coin.collected) {
                    ctx.fillStyle = '#FFD700';
                    ctx.fillRect(coin.x, coin.y, 16, 16);
                    ctx.strokeStyle = '#FFA500';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(coin.x, coin.y, 16, 16);
                    
                    // Label
                    ctx.fillStyle = '#000';
                    ctx.font = '12px Arial';
                    ctx.fillText(coin.label, coin.x, coin.y - 5);
                }
            });

            // Player
            ctx.fillStyle = player.onGround ? '#0066FF' : '#FF6600';
            ctx.fillRect(player.x, player.y, player.width, player.height);
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 2;
            ctx.strokeRect(player.x, player.y, player.width, player.height);

            // Jump arc visualization
            if (!player.onGround) {
                const maxHeight = player.startY - player.maxJumpHeight;
                ctx.strokeStyle = '#FF0000';
                ctx.lineWidth = 1;
                ctx.setLineDash([5, 5]);
                ctx.beginPath();
                ctx.moveTo(0, maxHeight);
                ctx.lineTo(canvas.width, maxHeight);
                ctx.stroke();
                ctx.setLineDash([]);
            }
        }

        let lastTime = 0;
        function gameLoop(time) {
            const dt = Math.min((time - lastTime) / 1000, 0.016);
            lastTime = time;

            update(dt);
            render();

            requestAnimationFrame(gameLoop);
        }

        console.log('Starting jump height test...');
        requestAnimationFrame(gameLoop);
    </script>
</body>
</html>
