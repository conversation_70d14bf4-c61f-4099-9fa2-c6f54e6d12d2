/**
 * Math utilities for the game
 */

export class MathUtils {
    /**
     * Clamp a value between min and max
     */
    static clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    }
    
    /**
     * Linear interpolation between two values
     */
    static lerp(start, end, factor) {
        return start + (end - start) * factor;
    }
    
    /**
     * Check if a value is approximately equal to another (within epsilon)
     */
    static approximately(a, b, epsilon = 0.001) {
        return Math.abs(a - b) < epsilon;
    }
    
    /**
     * Convert degrees to radians
     */
    static degToRad(degrees) {
        return degrees * Math.PI / 180;
    }
    
    /**
     * Convert radians to degrees
     */
    static radToDeg(radians) {
        return radians * 180 / Math.PI;
    }
    
    /**
     * Get distance between two points
     */
    static distance(x1, y1, x2, y2) {
        const dx = x2 - x1;
        const dy = y2 - y1;
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    /**
     * Get squared distance between two points (faster than distance)
     */
    static distanceSquared(x1, y1, x2, y2) {
        const dx = x2 - x1;
        const dy = y2 - y1;
        return dx * dx + dy * dy;
    }
    
    /**
     * Normalize a vector
     */
    static normalize(x, y) {
        const length = Math.sqrt(x * x + y * y);
        if (length === 0) return { x: 0, y: 0 };
        return { x: x / length, y: y / length };
    }
    
    /**
     * Get the sign of a number (-1, 0, or 1)
     */
    static sign(value) {
        return value > 0 ? 1 : value < 0 ? -1 : 0;
    }
    
    /**
     * Apply friction/damping to a value
     */
    static applyFriction(value, friction, dt) {
        const sign = this.sign(value);
        const absValue = Math.abs(value);
        const newAbsValue = Math.max(0, absValue - friction * dt);
        return newAbsValue * sign;
    }
    
    /**
     * Smooth step function (smooth interpolation)
     */
    static smoothStep(edge0, edge1, x) {
        const t = this.clamp((x - edge0) / (edge1 - edge0), 0, 1);
        return t * t * (3 - 2 * t);
    }
    
    /**
     * Random number between min and max
     */
    static random(min, max) {
        return Math.random() * (max - min) + min;
    }
    
    /**
     * Random integer between min and max (inclusive)
     */
    static randomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }
    
    /**
     * Check if a point is inside a rectangle
     */
    static pointInRect(px, py, rx, ry, rw, rh) {
        return px >= rx && px <= rx + rw && py >= ry && py <= ry + rh;
    }
    
    /**
     * Wrap a value around a range
     */
    static wrap(value, min, max) {
        const range = max - min;
        if (range <= 0) return min;
        
        let result = value;
        while (result < min) result += range;
        while (result >= max) result -= range;
        return result;
    }
    
    /**
     * Map a value from one range to another
     */
    static map(value, fromMin, fromMax, toMin, toMax) {
        const fromRange = fromMax - fromMin;
        const toRange = toMax - toMin;
        const scaledValue = (value - fromMin) / fromRange;
        return toMin + scaledValue * toRange;
    }
    
    /**
     * Ease in/out functions for smooth animations
     */
    static easeInOut(t) {
        return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
    }
    
    static easeIn(t) {
        return t * t;
    }
    
    static easeOut(t) {
        return t * (2 - t);
    }
}

/**
 * 2D Vector class
 */
export class Vector2 {
    constructor(x = 0, y = 0) {
        this.x = x;
        this.y = y;
    }
    
    /**
     * Add another vector to this vector
     */
    add(other) {
        this.x += other.x;
        this.y += other.y;
        return this;
    }
    
    /**
     * Subtract another vector from this vector
     */
    subtract(other) {
        this.x -= other.x;
        this.y -= other.y;
        return this;
    }
    
    /**
     * Multiply this vector by a scalar
     */
    multiply(scalar) {
        this.x *= scalar;
        this.y *= scalar;
        return this;
    }
    
    /**
     * Get the length of this vector
     */
    length() {
        return Math.sqrt(this.x * this.x + this.y * this.y);
    }
    
    /**
     * Get the squared length of this vector
     */
    lengthSquared() {
        return this.x * this.x + this.y * this.y;
    }
    
    /**
     * Normalize this vector
     */
    normalize() {
        const length = this.length();
        if (length > 0) {
            this.x /= length;
            this.y /= length;
        }
        return this;
    }
    
    /**
     * Set the values of this vector
     */
    set(x, y) {
        this.x = x;
        this.y = y;
        return this;
    }
    
    /**
     * Create a copy of this vector
     */
    clone() {
        return new Vector2(this.x, this.y);
    }
    
    /**
     * Get the dot product with another vector
     */
    dot(other) {
        return this.x * other.x + this.y * other.y;
    }
    
    /**
     * Static methods for creating vectors
     */
    static zero() {
        return new Vector2(0, 0);
    }
    
    static one() {
        return new Vector2(1, 1);
    }
    
    static up() {
        return new Vector2(0, -1);
    }
    
    static down() {
        return new Vector2(0, 1);
    }
    
    static left() {
        return new Vector2(-1, 0);
    }
    
    static right() {
        return new Vector2(1, 0);
    }
}
